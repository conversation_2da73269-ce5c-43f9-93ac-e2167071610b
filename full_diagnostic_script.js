// ПОЛНАЯ ДИАГНОСТИКА СКРИПТА ОТПИСКИ INSTAGRAM
// Запустите этот скрипт в консоли браузера для выявления проблем

console.log("🚀 ЗАПУСК ПОЛНОЙ ДИАГНОСТИКИ СКРИПТА ОТПИСКИ");

const DIAGNOSTIC_CONFIG = {
    UNFOLLOW_BUTTON_TEXT_RU: "Подписки",
    UNFOLLOW_BUTTON_TEXT_EN: "Following",
    CONFIRM_UNFOLLOW_ALTERNATIVES: [
        "Отменить подписку",
        "Unfollow", 
        "Отписаться",
        "Перестать читать",
        "Не читать",
        "Отмена подписки"
    ],
    EXCLUDED_USERS: ["e.pavlyuk_", "es<PERSON>enkoyana93", "julia_figulia", "irok_nu_ti_chego", "pa.vel6627rus"]
};

// Этап 1: Проверка модального окна
function checkModalWindow() {
    console.log("\n=== ЭТАП 1: ПРОВЕРКА МОДАЛЬНОГО ОКНА ===");
    
    const modal = document.querySelector('div[role="dialog"]');
    if (!modal) {
        console.error("❌ Модальное окно не найдено!");
        console.log("💡 Убедитесь, что вы находитесь на странице списка подписок");
        return false;
    }
    
    console.log("✅ Модальное окно найдено");
    console.log("📋 Модальное окно:", modal.outerHTML.substring(0, 200) + "...");
    
    const isVisible = modal.offsetParent !== null;
    console.log(`👁️ Модальное окно видимо: ${isVisible}`);
    
    return { modal, isVisible };
}

// Этап 2: Поиск кнопок "Подписки"
function checkUnfollowButtons(modal) {
    console.log("\n=== ЭТАП 2: ПОИСК КНОПОК 'ПОДПИСКИ' ===");
    
    if (!modal) {
        console.error("❌ Модальное окно не передано");
        return false;
    }
    
    const allButtons = Array.from(modal.querySelectorAll('button'));
    console.log(`🔘 Всего кнопок в модальном окне: ${allButtons.length}`);
    
    // Показываем все кнопки для анализа
    allButtons.forEach((btn, index) => {
        console.log(`🔘 Кнопка ${index + 1}: "${btn.textContent.trim()}" (visible: ${btn.offsetParent !== null})`);
    });
    
    // Ищем кнопки "Подписки"
    const unfollowButtons = allButtons.filter(btn => 
        btn.textContent.includes(DIAGNOSTIC_CONFIG.UNFOLLOW_BUTTON_TEXT_RU) || 
        btn.textContent.includes(DIAGNOSTIC_CONFIG.UNFOLLOW_BUTTON_TEXT_EN)
    );
    
    console.log(`🎯 Найдено кнопок "Подписки": ${unfollowButtons.length}`);
    
    if (unfollowButtons.length === 0) {
        console.error("❌ Кнопки 'Подписки' не найдены!");
        console.log("💡 Возможные причины:");
        console.log("   - Изменился текст кнопки в Instagram");
        console.log("   - Вы не на странице списка подписок");
        console.log("   - Нет пользователей для отписки");
        return false;
    }
    
    // Проверяем необработанные кнопки
    const unprocessedButtons = unfollowButtons.filter(btn => !btn.dataset.processed);
    console.log(`🔄 Необработанных кнопок: ${unprocessedButtons.length}`);
    
    if (unprocessedButtons.length === 0) {
        console.warn("⚠️ Все кнопки уже обработаны!");
        console.log("💡 Возможно, скрипт уже отработал или нужно обновить страницу");
    }
    
    return { unfollowButtons, unprocessedButtons };
}

// Этап 3: Тест извлечения имени пользователя
function testUsernameExtraction(button) {
    console.log("\n=== ЭТАП 3: ТЕСТ ИЗВЛЕЧЕНИЯ ИМЕНИ ПОЛЬЗОВАТЕЛЯ ===");
    
    if (!button) {
        console.error("❌ Кнопка не передана");
        return false;
    }
    
    console.log("🔍 Анализируемая кнопка:", button.outerHTML.substring(0, 200) + "...");
    
    // Множественные стратегии поиска родительского элемента
    const searchStrategies = [
        () => button.closest('div[style*="padding"]'),
        () => button.closest('[role="listitem"]'),
        () => button.closest('div').closest('div'),
        () => button.parentElement?.parentElement,
        () => button.parentElement?.parentElement?.parentElement,
        () => button.closest('div[class*="x"]'),
        () => {
            let current = button.parentElement;
            for (let i = 0; i < 5 && current; i++) {
                if (current.querySelector('a[href*="/"]')) return current;
                current = current.parentElement;
            }
            return null;
        }
    ];
    
    let userElement = null;
    for (let i = 0; i < searchStrategies.length; i++) {
        try {
            userElement = searchStrategies[i]();
            if (userElement) {
                console.log(`✅ Найден userElement стратегией ${i + 1}`);
                break;
            }
        } catch (e) {
            console.log(`❌ Стратегия ${i + 1} не сработала:`, e.message);
        }
    }
    
    if (!userElement) {
        console.error("❌ Родительский элемент пользователя не найден!");
        return false;
    }
    
    // Поиск ссылок на профиль
    const links = Array.from(userElement.querySelectorAll('a'));
    console.log(`🔗 Найдено ссылок: ${links.length}`);
    
    let username = null;
    links.forEach((link, index) => {
        console.log(`🔗 Ссылка ${index + 1}: href="${link.href}", text="${link.textContent.trim()}"`);
        
        if (link.href) {
            const regexes = [
                /\/([^\/\?#]+)\/?(?:\?.*)?(?:#.*)?$/,
                /\/([^\/\?#]+)\/?$/,
                /instagram\.com\/([^\/\?#]+)/,
                /\/([a-zA-Z0-9._]+)\/?/
            ];
            
            regexes.forEach((regex, regexIndex) => {
                const match = link.href.match(regex);
                if (match && match[1] && !username) {
                    const extractedName = match[1];
                    if (!extractedName.includes('explore') && !extractedName.includes('direct')) {
                        username = extractedName;
                        console.log(`✅ Извлечено имя пользователя regex ${regexIndex + 1}: "${username}"`);
                    }
                }
            });
        }
    });
    
    if (!username) {
        console.warn("⚠️ Имя пользователя не извлечено из ссылок");
        // Попробуем извлечь из текста
        const textElements = Array.from(userElement.querySelectorAll('*')).filter(el => 
            el.textContent && 
            el.textContent.trim().length > 0 && 
            el.textContent.trim().length < 50 &&
            !el.querySelector('*')
        );
        
        for (const el of textElements) {
            const text = el.textContent.trim().replace('@', '');
            if (text.match(/^[a-zA-Z0-9._]+$/) && text.length > 2) {
                username = text;
                console.log(`✅ Извлечено имя из текста: "${username}"`);
                break;
            }
        }
    }
    
    return username;
}

// Этап 4: Проверка системы исключений
function checkExclusionSystem(username) {
    console.log("\n=== ЭТАП 4: ПРОВЕРКА СИСТЕМЫ ИСКЛЮЧЕНИЙ ===");
    
    if (!username) {
        console.error("❌ Имя пользователя не определено!");
        return { excluded: false, reason: "username not found" };
    }
    
    console.log(`👤 Проверяемый пользователь: "${username}"`);
    console.log("📋 Список исключений:", DIAGNOSTIC_CONFIG.EXCLUDED_USERS);
    
    const isExcluded = DIAGNOSTIC_CONFIG.EXCLUDED_USERS.includes(username);
    console.log(`🎯 Результат: ${isExcluded ? '🚨 ИСКЛЮЧЕН' : '✅ НЕ ИСКЛЮЧЕН'}`);
    
    return { excluded: isExcluded, username };
}

// Этап 5: Симуляция клика по кнопке "Подписки"
function simulateUnfollowClick(button, username) {
    console.log("\n=== ЭТАП 5: СИМУЛЯЦИЯ КЛИКА ПО КНОПКЕ 'ПОДПИСКИ' ===");
    
    if (!button) {
        console.error("❌ Кнопка не передана");
        return false;
    }
    
    const exclusionCheck = checkExclusionSystem(username);
    if (exclusionCheck.excluded) {
        console.log("🚨 ПОЛЬЗОВАТЕЛЬ В СПИСКЕ ИСКЛЮЧЕНИЙ - КЛИК ЗАБЛОКИРОВАН!");
        return 'blocked_by_exclusion';
    }
    
    console.log("✅ Пользователь НЕ в списке исключений - клик разрешен");
    console.log("⚠️ ВНИМАНИЕ: Реальный клик НЕ выполняется для безопасности!");
    console.log("💡 Для выполнения реального клика используйте: button.click()");
    
    return 'click_allowed';
}

// Этап 6: Проверка кнопки подтверждения (запускается после клика)
function checkConfirmButton() {
    console.log("\n=== ЭТАП 6: ПРОВЕРКА КНОПКИ ПОДТВЕРЖДЕНИЯ ===");
    
    const dialogs = Array.from(document.querySelectorAll('div[role="dialog"]'));
    console.log(`📋 Найдено диалоговых окон: ${dialogs.length}`);
    
    const visibleDialogs = dialogs.filter(dialog => dialog.offsetParent !== null);
    console.log(`👁️ Видимых диалоговых окон: ${visibleDialogs.length}`);
    
    if (visibleDialogs.length === 0) {
        console.error("❌ Нет видимых диалоговых окон!");
        console.log("💡 Возможно, диалог подтверждения не появился после клика");
        return false;
    }
    
    let confirmButton = null;
    let foundDialog = null;
    
    visibleDialogs.forEach((dialog, dialogIndex) => {
        console.log(`\n--- АНАЛИЗ ДИАЛОГА ${dialogIndex + 1} ---`);
        const buttons = Array.from(dialog.querySelectorAll('button'));
        console.log(`🔘 Кнопок в диалоге: ${buttons.length}`);
        
        buttons.forEach((btn, btnIndex) => {
            const text = btn.textContent.trim();
            console.log(`🔘 Кнопка ${btnIndex + 1}: "${text}"`);
            
            // Проверяем соответствие критериям
            const exactMatch = DIAGNOSTIC_CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.includes(text);
            const partialMatch = DIAGNOSTIC_CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.some(alt => 
                text.includes(alt) || alt.includes(text)
            );
            const keywords = ['отмен', 'unfollow', 'отпис', 'перестать', 'читать'];
            const keywordMatch = keywords.some(keyword => 
                text.toLowerCase().includes(keyword.toLowerCase())
            );
            
            if ((exactMatch || partialMatch || keywordMatch) && !confirmButton) {
                confirmButton = btn;
                foundDialog = dialog;
                console.log(`✅ КНОПКА ПОДТВЕРЖДЕНИЯ НАЙДЕНА: "${text}"`);
            }
        });
    });
    
    return { confirmButton, foundDialog };
}

// Основная функция диагностики
function runFullDiagnostics() {
    console.log("🚀 ЗАПУСК ПОЛНОЙ ДИАГНОСТИКИ");
    
    // Этап 1: Модальное окно
    const modalCheck = checkModalWindow();
    if (!modalCheck) return;
    
    // Этап 2: Кнопки "Подписки"
    const buttonCheck = checkUnfollowButtons(modalCheck.modal);
    if (!buttonCheck) return;
    
    if (buttonCheck.unprocessedButtons.length === 0) {
        console.log("⚠️ Нет необработанных кнопок для тестирования");
        return;
    }
    
    // Этап 3: Извлечение имени пользователя
    const firstButton = buttonCheck.unprocessedButtons[0];
    const username = testUsernameExtraction(firstButton);
    
    // Этап 4: Система исключений
    const exclusionResult = checkExclusionSystem(username);
    
    // Этап 5: Симуляция клика
    const clickResult = simulateUnfollowClick(firstButton, username);
    
    // Этап 6: Кнопка подтверждения (только если есть диалог)
    const confirmResult = checkConfirmButton();
    
    // Итоговый отчет
    console.log("\n=== ИТОГОВЫЙ ОТЧЕТ ДИАГНОСТИКИ ===");
    console.log(`✅ Модальное окно: ${modalCheck ? 'НАЙДЕНО' : 'НЕ НАЙДЕНО'}`);
    console.log(`✅ Кнопки "Подписки": ${buttonCheck ? buttonCheck.unfollowButtons.length : 0}`);
    console.log(`✅ Необработанные кнопки: ${buttonCheck ? buttonCheck.unprocessedButtons.length : 0}`);
    console.log(`✅ Извлечение имени: ${username ? `"${username}"` : 'НЕ УДАЛОСЬ'}`);
    console.log(`✅ Система исключений: ${exclusionResult.excluded ? '🚨 ИСКЛЮЧЕН' : '✅ РАЗРЕШЕН'}`);
    console.log(`✅ Клик разрешен: ${clickResult === 'click_allowed' ? 'ДА' : 'НЕТ'}`);
    console.log(`✅ Кнопка подтверждения: ${confirmResult.confirmButton ? 'НАЙДЕНА' : 'НЕ НАЙДЕНА'}`);
    
    if (confirmResult.confirmButton) {
        console.log(`📝 Текст кнопки подтверждения: "${confirmResult.confirmButton.textContent.trim()}"`);
    }
    
    return {
        modal: modalCheck,
        buttons: buttonCheck,
        username: username,
        exclusion: exclusionResult,
        click: clickResult,
        confirm: confirmResult
    };
}

// Экспорт функций
window.fullDiagnostic = {
    checkModalWindow,
    checkUnfollowButtons,
    testUsernameExtraction,
    checkExclusionSystem,
    simulateUnfollowClick,
    checkConfirmButton,
    runFullDiagnostics
};

// Автоматический запуск
const diagnosticResult = runFullDiagnostics();

console.log("\n🔧 Функции диагностики доступны в window.fullDiagnostic");
console.log("📖 Используйте window.fullDiagnostic.runFullDiagnostics() для повторного запуска");
