// ОТЛАДОЧНЫЙ СКРИПТ ДЛЯ ДИАГНОСТИКИ КНОПКИ ПОДТВЕРЖДЕНИЯ
// Запустите этот скрипт в консоли браузера ПОСЛЕ клика на кнопку "Подписки"

console.log("🔧 ЗАПУСК ДИАГНОСТИКИ КНОПКИ ПОДТВЕРЖДЕНИЯ");

const DEBUG_CONFIG = {
    CONFIRM_UNFOLLOW_ALTERNATIVES: [
        "Отменить подписку",
        "Unfollow", 
        "Отписаться",
        "Перестать читать",
        "Не читать",
        "Отмена подписки"
    ]
};

// Функция для поиска всех диалоговых окон
function findAllDialogs() {
    console.log("🔍 ПОИСК ВСЕХ ДИАЛОГОВЫХ ОКОН");
    
    const allDialogs = Array.from(document.querySelectorAll('div[role="dialog"]'));
    console.log(`📊 Найдено диалоговых окон: ${allDialogs.length}`);
    
    allDialogs.forEach((dialog, index) => {
        const isVisible = dialog.offsetParent !== null;
        console.log(`📋 Диалог ${index + 1}:`, {
            visible: isVisible,
            html: dialog.outerHTML.substring(0, 200) + "...",
            buttons: dialog.querySelectorAll('button').length
        });
    });
    
    return allDialogs.filter(dialog => dialog.offsetParent !== null);
}

// Функция для анализа кнопок в диалоге
function analyzeButtonsInDialog(dialog, dialogIndex) {
    console.log(`🔍 АНАЛИЗ КНОПОК В ДИАЛОГЕ ${dialogIndex + 1}`);
    
    const buttons = Array.from(dialog.querySelectorAll('button'));
    console.log(`🔘 Найдено кнопок в диалоге: ${buttons.length}`);
    
    buttons.forEach((btn, btnIndex) => {
        const text = btn.textContent.trim();
        const isVisible = btn.offsetParent !== null;
        
        console.log(`🔘 Кнопка ${btnIndex + 1}:`, {
            text: text,
            visible: isVisible,
            disabled: btn.disabled,
            classes: btn.className,
            html: btn.outerHTML.substring(0, 150) + "..."
        });
        
        // Проверяем, подходит ли эта кнопка под критерии поиска
        const exactMatch = DEBUG_CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.includes(text);
        const partialMatch = DEBUG_CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.some(alt => 
            text.includes(alt) || alt.includes(text)
        );
        const keywords = ['отмен', 'unfollow', 'отпис', 'перестать', 'читать'];
        const keywordMatch = keywords.some(keyword => 
            text.toLowerCase().includes(keyword.toLowerCase())
        );
        
        if (exactMatch || partialMatch || keywordMatch) {
            console.log(`✅ ПОТЕНЦИАЛЬНАЯ КНОПКА ПОДТВЕРЖДЕНИЯ НАЙДЕНА!`, {
                exactMatch,
                partialMatch,
                keywordMatch,
                text: text
            });
        }
    });
    
    return buttons;
}

// Функция для поиска кнопки подтверждения
function findConfirmButton() {
    console.log("🎯 ПОИСК КНОПКИ ПОДТВЕРЖДЕНИЯ");
    
    const visibleDialogs = findAllDialogs();
    
    if (visibleDialogs.length === 0) {
        console.error("❌ Нет видимых диалоговых окон!");
        return null;
    }
    
    let foundButton = null;
    let foundDialog = null;
    
    visibleDialogs.forEach((dialog, dialogIndex) => {
        console.log(`\n--- АНАЛИЗ ДИАЛОГА ${dialogIndex + 1} ---`);
        const buttons = analyzeButtonsInDialog(dialog, dialogIndex);
        
        const confirmButton = buttons.find(btn => {
            const buttonText = btn.textContent.trim();
            
            // Точное совпадение
            const exactMatch = DEBUG_CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.includes(buttonText);
            if (exactMatch) {
                console.log(`✅ Найдено точное совпадение: "${buttonText}"`);
                return true;
            }
            
            // Частичное совпадение
            const partialMatch = DEBUG_CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.some(alt => 
                buttonText.includes(alt) || alt.includes(buttonText)
            );
            if (partialMatch) {
                console.log(`✅ Найдено частичное совпадение: "${buttonText}"`);
                return true;
            }
            
            // Ключевые слова
            const keywords = ['отмен', 'unfollow', 'отпис', 'перестать', 'читать'];
            const keywordMatch = keywords.some(keyword => 
                buttonText.toLowerCase().includes(keyword.toLowerCase())
            );
            if (keywordMatch) {
                console.log(`✅ Найдено совпадение по ключевому слову: "${buttonText}"`);
                return true;
            }
            
            return false;
        });
        
        if (confirmButton && !foundButton) {
            foundButton = confirmButton;
            foundDialog = dialog;
            console.log(`🎯 КНОПКА ПОДТВЕРЖДЕНИЯ НАЙДЕНА В ДИАЛОГЕ ${dialogIndex + 1}!`);
        }
    });
    
    return { button: foundButton, dialog: foundDialog };
}

// Функция для симуляции клика на кнопку подтверждения
function simulateConfirmClick() {
    console.log("🎬 СИМУЛЯЦИЯ КЛИКА НА КНОПКУ ПОДТВЕРЖДЕНИЯ");
    
    const result = findConfirmButton();
    
    if (!result.button) {
        console.error("❌ Кнопка подтверждения не найдена! Клик невозможен.");
        return false;
    }
    
    console.log("✅ Кнопка найдена, выполняем клик...");
    console.log("🔘 Кнопка:", result.button);
    console.log("📋 Диалог:", result.dialog);
    
    // НЕ выполняем реальный клик, только показываем что нашли
    console.log("⚠️ ВНИМАНИЕ: Реальный клик НЕ выполнен для безопасности!");
    console.log("💡 Для выполнения реального клика используйте: result.button.click()");
    
    return true;
}

// Функция для поиска всех подозрительных кнопок на странице
function findAllSuspiciousButtons() {
    console.log("🔍 ПОИСК ВСЕХ ПОДОЗРИТЕЛЬНЫХ КНОПОК НА СТРАНИЦЕ");
    
    const allButtons = Array.from(document.querySelectorAll('button'));
    console.log(`📊 Всего кнопок на странице: ${allButtons.length}`);
    
    const suspiciousButtons = allButtons.filter(btn => {
        const text = btn.textContent.trim().toLowerCase();
        return text.length > 0 && text.length < 50 && (
            text.includes('отмен') || 
            text.includes('unfollow') || 
            text.includes('отпис') || 
            text.includes('перестать') ||
            text.includes('читать') ||
            text.includes('подпис') ||
            text.includes('follow')
        );
    });
    
    console.log(`🔍 Подозрительных кнопок: ${suspiciousButtons.length}`);
    
    suspiciousButtons.forEach((btn, index) => {
        console.log(`🔘 Подозрительная кнопка ${index + 1}:`, {
            text: btn.textContent.trim(),
            visible: btn.offsetParent !== null,
            disabled: btn.disabled,
            inDialog: !!btn.closest('div[role="dialog"]')
        });
    });
    
    return suspiciousButtons;
}

// Основная функция диагностики
function runConfirmButtonDiagnostics() {
    console.log("🚀 ЗАПУСК ПОЛНОЙ ДИАГНОСТИКИ КНОПКИ ПОДТВЕРЖДЕНИЯ");
    
    console.log("\n=== ШАГ 1: ПОИСК ДИАЛОГОВЫХ ОКОН ===");
    const dialogs = findAllDialogs();
    
    console.log("\n=== ШАГ 2: ПОИСК КНОПКИ ПОДТВЕРЖДЕНИЯ ===");
    const confirmResult = findConfirmButton();
    
    console.log("\n=== ШАГ 3: ПОИСК ВСЕХ ПОДОЗРИТЕЛЬНЫХ КНОПОК ===");
    const suspiciousButtons = findAllSuspiciousButtons();
    
    console.log("\n=== РЕЗУЛЬТАТЫ ДИАГНОСТИКИ ===");
    console.log(`📋 Диалоговых окон: ${dialogs.length}`);
    console.log(`🎯 Кнопка подтверждения найдена: ${confirmResult.button ? 'ДА' : 'НЕТ'}`);
    console.log(`🔍 Подозрительных кнопок: ${suspiciousButtons.length}`);
    
    if (confirmResult.button) {
        console.log("✅ ДИАГНОСТИКА УСПЕШНА: Кнопка подтверждения найдена!");
        console.log("💡 Используйте simulateConfirmClick() для тестирования клика");
    } else {
        console.log("❌ ПРОБЛЕМА: Кнопка подтверждения НЕ найдена!");
        console.log("💡 Проверьте подозрительные кнопки выше");
        console.log("💡 Возможно, нужно обновить список CONFIRM_UNFOLLOW_ALTERNATIVES");
    }
}

// Экспорт функций для ручного использования
window.debugConfirmButton = {
    findAllDialogs,
    analyzeButtonsInDialog,
    findConfirmButton,
    simulateConfirmClick,
    findAllSuspiciousButtons,
    runConfirmButtonDiagnostics
};

// Автоматический запуск диагностики
runConfirmButtonDiagnostics();

console.log("\n🔧 Отладочные функции доступны в window.debugConfirmButton");
console.log("📖 Используйте window.debugConfirmButton.runConfirmButtonDiagnostics() для повторного запуска");
console.log("🎬 Используйте window.debugConfirmButton.simulateConfirmClick() для тестирования клика");
