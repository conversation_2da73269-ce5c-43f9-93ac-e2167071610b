// ПРОСТОЙ ТЕСТ ЛОГИКИ ИСКЛЮЧЕНИЙ
// Запустите в консоли браузера для проверки базовой логики

console.log("🧪 ТЕСТ ЛОГИКИ ИСКЛЮЧЕНИЙ");

const TEST_CONFIG = {
    EXCLUDED_USERS: ["e.pavlyuk_", "esipenkoyana93", "julia_figulia", "irok_nu_ti_chego", "pa.vel6627rus"]
};

// Тестовые данные
const testUsers = [
    "e.pavlyuk_",           // Должен быть исключен
    "esipenkoyana93",       // Должен быть исключен  
    "julia_figulia",        // Должен быть исключен
    "irok_nu_ti_chego",     // Должен быть исключен
    "pa.vel6627rus",        // Должен быть исключен
    "random_user_123",      // НЕ должен быть исключен
    "another_user",         // НЕ должен быть исключен
    "test_account",         // НЕ должен быть исключен
    null,                   // Особый случай
    undefined,              // Особый случай
    "",                     // Особый случай
    "e.pavlyuk",            // Похожий, но НЕ точное совпадение
    "E.PAVLYUK_"            // Другой регистр
];

console.log("📋 Список исключенных пользователей:", TEST_CONFIG.EXCLUDED_USERS);
console.log("🧪 Начинаем тестирование...");

let passedTests = 0;
let failedTests = 0;

testUsers.forEach((username, index) => {
    console.log(`\n--- Тест ${index + 1}: "${username}" ---`);
    
    const isExcluded = username ? TEST_CONFIG.EXCLUDED_USERS.includes(username) : false;
    const shouldBeExcluded = TEST_CONFIG.EXCLUDED_USERS.includes(username);
    
    console.log(`Пользователь: ${username}`);
    console.log(`Результат проверки: ${isExcluded ? '🚨 ИСКЛЮЧЕН' : '✅ НЕ ИСКЛЮЧЕН'}`);
    console.log(`Ожидаемый результат: ${shouldBeExcluded ? '🚨 ИСКЛЮЧЕН' : '✅ НЕ ИСКЛЮЧЕН'}`);
    
    if (isExcluded === shouldBeExcluded) {
        console.log(`✅ ТЕСТ ПРОЙДЕН`);
        passedTests++;
    } else {
        console.log(`❌ ТЕСТ НЕ ПРОЙДЕН`);
        failedTests++;
    }
});

console.log(`\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:`);
console.log(`✅ Пройдено тестов: ${passedTests}`);
console.log(`❌ Не пройдено тестов: ${failedTests}`);
console.log(`📈 Процент успеха: ${Math.round((passedTests / (passedTests + failedTests)) * 100)}%`);

if (failedTests === 0) {
    console.log(`🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Логика исключений работает корректно.`);
} else {
    console.log(`⚠️ ЕСТЬ ПРОБЛЕМЫ! Проверьте логику исключений.`);
}

// Дополнительный тест: проверка чувствительности к регистру
console.log(`\n🔤 ТЕСТ ЧУВСТВИТЕЛЬНОСТИ К РЕГИСТРУ:`);
const caseSensitiveTests = [
    { input: "e.pavlyuk_", expected: true },
    { input: "E.PAVLYUK_", expected: false },
    { input: "E.pavlyuk_", expected: false },
    { input: "e.PAVLYUK_", expected: false }
];

caseSensitiveTests.forEach(test => {
    const result = TEST_CONFIG.EXCLUDED_USERS.includes(test.input);
    const passed = result === test.expected;
    console.log(`"${test.input}" → ${result ? 'исключен' : 'не исключен'} (ожидалось: ${test.expected ? 'исключен' : 'не исключен'}) ${passed ? '✅' : '❌'}`);
});

// Функция для ручного тестирования
window.testExclusion = function(username) {
    console.log(`\n🧪 РУЧНОЙ ТЕСТ: "${username}"`);
    const isExcluded = username ? TEST_CONFIG.EXCLUDED_USERS.includes(username) : false;
    console.log(`Результат: ${isExcluded ? '🚨 ИСКЛЮЧЕН' : '✅ НЕ ИСКЛЮЧЕН'}`);
    return isExcluded;
};

console.log(`\n🔧 Для ручного тестирования используйте: window.testExclusion("имя_пользователя")`);

// Тест производительности
console.log(`\n⚡ ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ:`);
const startTime = performance.now();
for (let i = 0; i < 10000; i++) {
    TEST_CONFIG.EXCLUDED_USERS.includes("e.pavlyuk_");
}
const endTime = performance.now();
console.log(`10,000 проверок выполнено за ${(endTime - startTime).toFixed(2)} мс`);

console.log(`\n🏁 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО`);
