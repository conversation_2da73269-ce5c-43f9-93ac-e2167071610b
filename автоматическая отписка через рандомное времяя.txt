// --- CONFIGURATION START --- //
const CONFIG = {
    SCROLL_STEP: 50,
    SCROLL_PAUSE: 2000,
    MIN_RANDOM_DELAY: 1000,
    MAX_RANDOM_DELAY: 3000,
    PAGE_LOAD_WAIT: 10000,
    MOD<PERSON>_OPEN_RETRY_DELAY: 2000,
    MODAL_OPEN_MAX_RETRIES: 10,
    UNFOLLOW_CLICK_DELAY: 4000, // Увеличена пауза для появления окна подтверждения
    CONFIRM_MIN_DELAY: 2500,    // Увеличена задержка до 2.5-4.5 секунд
    CONFIRM_MAX_DELAY: 4500,
    POST_UNFOLLOW_DELAY: 3000,  // Увеличена пауза для завершения отписки
    CONFIRM_CHECK_RETRIES: 3,
    MODAL_CLOSE_DELAY: 3000,    // Увеличена пауза до 3 секунд
    MODAL_CLOSE_CHECK_RETRIES: 3,
    DEFAULT_MAX_UNFOLLOWS: 10,
    PROMPT_MESSAGE: "Введите максимальное количество отписок (по умолчанию 10):",
    PROMPT_MESSAGE_UNLIMITED: "Введите максимальное количество отписок (0 = неограниченно)",

    // Селекторы (могут потребовать обновления при изменении верстки Instagram)
    MODAL_DIALOG_SELECTOR: 'div[role="dialog"]',
    FOLLOWING_BUTTON_SELECTOR: 'a[href*="/following/"]',
    UNFOLLOW_BUTTON_TEXT_RU: "Подписки",
    UNFOLLOW_BUTTON_TEXT_EN: "Following",
    // CONFIRM_UNFOLLOW_BUTTON_SELECTOR: 'button._a9--._ap36._a9-_:not([disabled])', // Этот селектор может быть нестабильным - заменен на текстовый поиск
    CONFIRM_UNFOLLOW_TEXT_RU: "Отменить подписку", // Обновлено согласно информации от пользователя
    CONFIRM_UNFOLLOW_TEXT_EN: "Unfollow", // Предполагаемый английский эквивалент для кнопки подтверждения
    // Дополнительные варианты текста кнопки подтверждения
    CONFIRM_UNFOLLOW_ALTERNATIVES: [
        "Отменить подписку",
        "Unfollow",
        "Отписаться",
        "Перестать читать",
        "Не читать",
        "Отмена подписки"
    ],
    CLOSE_MODAL_BUTTON_SELECTOR: 'div[role="dialog"] [aria-label="Закрыть"]',
    SCROLLABLE_MODAL_CONTENT_SELECTOR_1: 'div[role="dialog"] div.x83m0k',
    SCROLLABLE_MODAL_CONTENT_SELECTOR_2: 'div[role="dialog"] div[style*="overflow"]',
    SCROLLABLE_MODAL_CONTENT_SELECTOR_3: 'div[role="dialog"] .x1r19xyu div[style*="overflow"]',
    USER_PROFILE_LINK_SELECTOR: 'a[href*="/"]:not([href*="/explore/"]):not([href*="/direct/"]):not([href*="/accounts/"]):not([href*="/help/"]):not([href*="/about/"])', // Селектор для ссылки на профиль пользователя в строке списка
    USERNAME_IN_PROFILE_LINK_REGEX: /\/([^\/\?#]+)\/?(?:\?.*)?(?:#.*)?$/, // Улучшенное регулярное выражение для извлечения имени пользователя из URL профиля
    EXCLUDED_USERS: ["e.pavlyuk_", "esipenkoyana93", "julia_figulia", "irok_nu_ti_chego", "pa.vel6627rus"], // Список пользователей, которых не нужно отписываться

    // Сообщения для логов
    LOG_SKIPPING_EXCLUDED_USER: "Пропускаем пользователя из списка исключений:",
    LOG_SCROLL_ELEMENT_NOT_FOUND: "Элемент для прокрутки не найден.",
    LOG_ELEMENT_NOT_SCROLLABLE: "Элемент не прокручиваем. Проверяем родительские элементы...",
    LOG_SCROLLABLE_ELEMENT_NOT_FOUND: "Не найден прокручиваемый элемент.",
    LOG_SCROLLABLE_ELEMENT_FOUND: "Найден прокручиваемый элемент:",
    LOG_SCROLLING_DOWN: "Прокручиваем вниз на",
    LOG_SCROLLING_DOWN_PIXELS: "пикселей...",
    LOG_SCROLL_COMPLETE: "Прокрутка выполнена. Текущая позиция:",
    LOG_CHECKING_CURRENT_URL: "Проверяем текущий URL...",
    LOG_INVALID_URL: "Ошибка: скрипт должен быть запущен на странице профиля Instagram (например, https://www.instagram.com/username/). Текущий URL:",
    LOG_CURRENT_URL: "Текущий URL:",
    LOG_WAITING_PAGE_LOAD: "Ожидаем загрузки страницы перед поиском кнопки...",
    LOG_CHECKING_MODAL_OPEN: "Проверяем, открыто ли модальное окно...",
    LOG_MODAL_ALREADY_OPEN: "Модальное окно уже открыто.",
    LOG_MODAL_NOT_OPEN_SEARCHING_BUTTON: "Модальное окно не открыто. Ищем кнопку для открытия списка подписок...",
    LOG_ATTEMPT: "Попытка",
    LOG_FOUND_POTENTIAL_BUTTONS: "Найдено",
    LOG_POTENTIAL_BUTTONS_SUFFIX: "потенциальных кнопок.",
    LOG_BUTTON_DETAILS_PREFIX: "Кнопка",
    LOG_BUTTON_FOUND: "Кнопка найдена:",
    LOG_BUTTON_NOT_FOUND_RETRYING: "Кнопка не найдена на попытке",
    LOG_WAITING_BEFORE_NEXT_ATTEMPT: "Ожидаем перед следующей попыткой...",
    LOG_FOLLOWING_LIST_BUTTON_NOT_FOUND: "Ошибка: кнопка для открытия списка подписок не найдена после",
    LOG_FOLLOWING_LIST_BUTTON_NOT_FOUND_SUFFIX: "попыток. Убедитесь, что вы на странице профиля.",
    LOG_CLICKING_FOLLOWING_LIST_BUTTON: "Нажимаем на кнопку для открытия списка подписок...",
    LOG_WAITING_MODAL_OPEN_ATTEMPT: "Ожидаем открытия модального окна... Попытка",
    LOG_MODAL_SUCCESSFULLY_OPENED: "Модальное окно успешно открыто.",
    LOG_MODAL_OPEN_FAILED: "Ошибка: модальное окно не открылось после",
    LOG_MODAL_OPEN_FAILED_SUFFIX: "попыток.",
    LOG_UNFOLLOW_BUTTONS_NOT_FOUND: "Кнопки 'Подписки' не найдены.",
    LOG_ALL_BUTTONS_PROCESSED: "Все кнопки уже обработаны.",
    LOG_CLICKING_UNFOLLOW: "Нажимаем 'Отписаться'...",
    LOG_CLICKING_CONFIRM_BUTTON: "Нажимаем кнопку подтверждения...",
    LOG_WAITING_UNFOLLOW_COMPLETION_ATTEMPT: "Ожидание завершения отписки... Попытка",
    LOG_UNFOLLOW_SUCCESSFUL: "Отписка завершена успешно.",
    LOG_CONFIRM_BUTTON_NOT_FOUND: "Кнопка подтверждения не найдена.",
    LOG_UNFOLLOW_FAILED: "Действие отписки не подтверждено.",
    LOG_SEARCHING_CLOSE_MODAL_BUTTON: "Ищем кнопку закрытия модального окна...",
    LOG_SVG_CLOSE_BUTTON_NOT_FOUND: "SVG кнопка закрытия модального окна не найдена.",
    LOG_CLICKABLE_PARENT_NOT_FOUND: "Кликабельный родительский элемент для кнопки закрытия не найден.",
    LOG_CLOSE_BUTTON_FOUND: "Кнопка закрытия найдена:",
    LOG_WAITING_MODAL_CLOSE_ATTEMPT: "Ожидание закрытия модального окна... Попытка",
    LOG_MODAL_SUCCESSFULLY_CLOSED: "Модальное окно успешно закрыто.",
    LOG_MODAL_NOT_CLOSED: "Модальное окно не закрылось.",
    LOG_ASKING_UNFOLLOW_COUNT: "Спрашиваем количество отписок...",
    LOG_STARTING_UNFOLLOWS_MAX: "Начало отписок. Максимум:",
    LOG_UNFOLLOWS_SUFFIX: "отписок.",
    LOG_OPENING_MODAL_WINDOW: "Открываем модальное окно...",
    LOG_FAILED_TO_OPEN_MODAL: "Не удалось открыть модальное окно. Прерываем процесс.",
    LOG_PROCESSING_UNFOLLOW_NUMBER: "Обработка отписки номер",
    LOG_SEARCHING_SCROLL_ELEMENT: "Ищем элемент для прокрутки в модальном окне...",
    LOG_SCROLL_ELEMENT_NOT_FOUND_IN_MODAL: "Элемент для прокрутки в модальном окне не найден.",
    LOG_PERFORMING_UNFOLLOW: "Выполняем отписку...",
    LOG_UNFOLLOW_ACTION_FAILED_RETRYING: "Попытка отписки не подтверждена. Пробуем прокрутить список для поиска других пользователей...",
    LOG_UNFOLLOW_SUCCESS_DELAYING: "Отписка успешна. Случайная задержка перед следующей отпиской...",
    LOG_MAX_UNFOLLOWS_REACHED: "Достигнуто максимальное количество отписок.",
    LOG_CLOSING_MODAL_WINDOW: "Закрываем модальное окно...",
    LOG_FAILED_TO_CLOSE_MODAL: "Не удалось закрыть модальное окно.",
    LOG_PROCESS_COMPLETE_UNFOLLOWED: "Процесс завершен. Отписано:",
    LOG_USERS: "пользователей.",
    LOG_SCRIPT_FINISHED: "Скрипт завершил работу.",
    LOG_ERROR_OCCURRED: "Произошла ошибка:",
    LOG_CHECKING_AND_OPENING_MODAL: "Проверяем и открываем модальное окно...",
    LOG_MODAL_ACTIVE_CONTINUING: "Модальное окно активно, продолжаем работу...",
    LOG_FAILED_TO_OPEN_MODAL_EXITING: "Не удалось открыть модальное окно. Завершаем скрипт.",
    LOG_ATTEMPT_FIND_SCROLL_ELEMENT: "Попытка",
    LOG_FIND_SCROLL_ELEMENT_SUFFIX: "найти элемент...",
    LOG_SCROLL_ELEMENT_NOT_FOUND_AFTER_ATTEMPTS: "Ошибка: элемент для прокрутки не найден после",
    LOG_SCROLL_ELEMENT_NOT_FOUND_AFTER_ATTEMPTS_SUFFIX: "попыток.",
    LOG_FOUND_SCROLL_ELEMENT: "Найден элемент для прокрутки:",
    LOG_UNFOLLOW_ACTION_FAILED_NO_MORE_BUTTONS: "Больше не удалось найти кнопки для отписки. Завершаем.",
    LOG_UNFOLLOW_COMPLETED_NUMBER: "Выполнена отписка #",
    LOG_UNFOLLOW_COMPLETED_OUT_OF: "из",
    LOG_WAITING_BEFORE_NEXT_UNFOLLOW: "Ожидание",
    LOG_SECONDS_BEFORE_NEXT_UNFOLLOW: "секунд перед следующей отпиской...",
    LOG_DONE_UNFOLLOWED: "Готово! Отписано:",
    LOG_STARTING_PROCESS_MAX_UNFOLLOWS: "Начинаем процесс с максимумом",
    LOG_UNFOLLOW_PROCESS_AND_CLOSE_MODAL_COMPLETE: "Процесс отписки и закрытие модального окна завершены.",
    LOG_USER_SKIPPED_EXCLUSION: "Пользователь пропущен (в списке исключений)."
};
// --- CONFIGURATION END --- //

// Функция безопасной прокрутки с возвратом в верхнюю часть
async function safeScroll(element, step = CONFIG.SCROLL_STEP) {
    if (!element) {
        console.error(CONFIG.LOG_SCROLL_ELEMENT_NOT_FOUND);
        return;
    }

    if (element.scrollHeight <= element.clientHeight) {
        console.warn(CONFIG.LOG_ELEMENT_NOT_SCROLLABLE);
        let parent = element.parentElement;
        while (parent && element.scrollHeight <= element.clientHeight) {
            element = parent;
            parent = element.parentElement;
        }
        if (element.scrollHeight <= element.clientHeight) {
            console.error(CONFIG.LOG_SCROLLABLE_ELEMENT_NOT_FOUND);
            return;
        }
        console.log(CONFIG.LOG_SCROLLABLE_ELEMENT_FOUND, element);
    }

    console.log(CONFIG.LOG_SCROLLING_DOWN, step, CONFIG.LOG_SCROLLING_DOWN_PIXELS);
    element.scrollBy({
        top: step,
        behavior: 'smooth'
    });
    await new Promise(resolve => setTimeout(resolve, CONFIG.SCROLL_PAUSE));
    console.log(CONFIG.LOG_SCROLL_COMPLETE, element.scrollTop);
}

// Функция генерации случайной задержки
function getRandomDelay(min = CONFIG.MIN_RANDOM_DELAY, max = CONFIG.MAX_RANDOM_DELAY) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Функция ожидания (асинхронная пауза)
async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Функция для открытия модального окна
async function openModalWindow() {
    console.log(CONFIG.LOG_CHECKING_CURRENT_URL);
    const currentUrl = window.location.href;
    if (!currentUrl.includes('/')) { // Простая проверка, можно улучшить для более точного определения страницы профиля
        console.error(CONFIG.LOG_INVALID_URL, currentUrl);
        return false;
    }
    console.log(CONFIG.LOG_CURRENT_URL, currentUrl);

    console.log(CONFIG.LOG_WAITING_PAGE_LOAD);
    await sleep(CONFIG.PAGE_LOAD_WAIT);

    console.log(CONFIG.LOG_CHECKING_MODAL_OPEN);
    let modalWindow = document.querySelector(CONFIG.MODAL_DIALOG_SELECTOR);
    if (modalWindow) {
        console.log(CONFIG.LOG_MODAL_ALREADY_OPEN);
        return true;
    }

    console.log(CONFIG.LOG_MODAL_NOT_OPEN_SEARCHING_BUTTON);
    let followButton = null;

    for (let i = 0; i < CONFIG.MODAL_OPEN_MAX_RETRIES; i++) {
        const potentialButtons = Array.from(document.querySelectorAll(CONFIG.FOLLOWING_BUTTON_SELECTOR));
        console.log(`${CONFIG.LOG_ATTEMPT} ${i + 1}: ${CONFIG.LOG_FOUND_POTENTIAL_BUTTONS} ${potentialButtons.length} ${CONFIG.LOG_POTENTIAL_BUTTONS_SUFFIX}`);

        potentialButtons.forEach((btn, index) => {
            console.log(`${CONFIG.LOG_BUTTON_DETAILS_PREFIX} ${index + 1}:`, {
                href: btn.getAttribute('href'),
                visible: btn.offsetParent !== null,
                classes: btn.className
            });
        });

        followButton = potentialButtons.find(a => a.offsetParent !== null);
        if (followButton) {
            console.log(CONFIG.LOG_BUTTON_FOUND, followButton);
            break;
        }

        console.log(`${CONFIG.LOG_BUTTON_NOT_FOUND_RETRYING} ${i + 1}. ${CONFIG.LOG_WAITING_BEFORE_NEXT_ATTEMPT}`);
        await sleep(CONFIG.MODAL_OPEN_RETRY_DELAY);
    }

    if (!followButton) {
        console.error(`${CONFIG.LOG_FOLLOWING_LIST_BUTTON_NOT_FOUND} ${CONFIG.MODAL_OPEN_MAX_RETRIES} ${CONFIG.LOG_FOLLOWING_LIST_BUTTON_NOT_FOUND_SUFFIX}`);
        return false;
    }

    console.log(CONFIG.LOG_CLICKING_FOLLOWING_LIST_BUTTON);
    followButton.click();
    await sleep(CONFIG.MODAL_OPEN_RETRY_DELAY);

    for (let i = 0; i < CONFIG.MODAL_OPEN_MAX_RETRIES; i++) {
        modalWindow = document.querySelector(CONFIG.MODAL_DIALOG_SELECTOR);
        if (modalWindow) {
            console.log(CONFIG.LOG_MODAL_SUCCESSFULLY_OPENED);
            await sleep(CONFIG.MODAL_OPEN_RETRY_DELAY); // Даем время на прогрузку содержимого модального окна
            return true;
        }
        console.log(`${CONFIG.LOG_WAITING_MODAL_OPEN_ATTEMPT} ${i + 1}`);
        await sleep(CONFIG.MODAL_OPEN_RETRY_DELAY);
    }

    console.error(`${CONFIG.LOG_MODAL_OPEN_FAILED} ${CONFIG.MODAL_OPEN_MAX_RETRIES} ${CONFIG.LOG_MODAL_OPEN_FAILED_SUFFIX}`);
    return false;
}

// Функция для выполнения одной отписки
async function performUnfollow(modalContentElement) { // Переименован параметр для ясности
    const allButtonsInModal = Array.from(modalContentElement.querySelectorAll('button')); // Ищем ВСЕ кнопки внутри переданного modalContentElement
    console.log("Найденные кнопки в модальном окне перед фильтрацией:", allButtonsInModal.length);
    allButtonsInModal.forEach((btn, index) => {
        console.log(`Кнопка ${index + 1}: textContent="${btn.textContent}", outerHTML="${btn.outerHTML.substring(0, 100)}..."`);
    });

    const followButtons = allButtonsInModal
        .filter(btn => btn.textContent.includes(CONFIG.UNFOLLOW_BUTTON_TEXT_RU) || btn.textContent.includes(CONFIG.UNFOLLOW_BUTTON_TEXT_EN));

    console.log("Кнопки после фильтрации по тексту:", followButtons.length);

    if (followButtons.length === 0) {
        console.log(CONFIG.LOG_UNFOLLOW_BUTTONS_NOT_FOUND);
        // Дополнительный лог для отладки, если кнопки не найдены после фильтрации
        console.log(`Ожидаемый текст RU: "${CONFIG.UNFOLLOW_BUTTON_TEXT_RU}"`);
        console.log(`Ожидаемый текст EN: "${CONFIG.UNFOLLOW_BUTTON_TEXT_EN}"`);
        return false;
    }

    const button = followButtons.find(btn => !btn.dataset.processed);
    if (!button) {
        console.log(CONFIG.LOG_ALL_BUTTONS_PROCESSED);
        console.log("🔍 DEBUG: Проверяем, есть ли необработанные кнопки после прокрутки...");

        // Показываем статистику обработанных кнопок
        const processedButtons = followButtons.filter(btn => btn.dataset.processed);
        console.log(`📊 Статистика кнопок: всего ${followButtons.length}, обработано ${processedButtons.length}, необработано ${followButtons.length - processedButtons.length}`);

        // Если все кнопки обработаны, возможно нужна прокрутка для загрузки новых
        if (processedButtons.length === followButtons.length && followButtons.length > 0) {
            console.log("💡 Все видимые кнопки обработаны. Возможно, нужна прокрутка для загрузки новых пользователей.");
        }

        return false;
    }

    // --- КРИТИЧЕСКАЯ ПРОВЕРКА НА ИСКЛЮЧЕННОГО ПОЛЬЗОВАТЕЛЯ --- //
    console.log("🔍 DEBUG: НАЧАЛО КРИТИЧЕСКОЙ ПРОВЕРКИ НА ИСКЛЮЧЕННОГО ПОЛЬЗОВАТЕЛЯ");
    console.log("🔍 DEBUG: Кнопка для проверки:", button.outerHTML.substring(0, 200));

    // МНОЖЕСТВЕННЫЕ СПОСОБЫ ПОИСКА РОДИТЕЛЬСКОГО ЭЛЕМЕНТА
    let userElement = null;
    const searchStrategies = [
        () => button.closest('div[style*="padding"]'),
        () => button.closest('[role="listitem"]'),
        () => button.closest('div').closest('div'),
        () => button.parentElement?.parentElement,
        () => button.parentElement?.parentElement?.parentElement,
        () => button.closest('div[class*="x"]'), // Instagram часто использует классы с 'x'
        () => {
            let current = button.parentElement;
            for (let i = 0; i < 5 && current; i++) {
                if (current.querySelector('a[href*="/"]')) return current;
                current = current.parentElement;
            }
            return null;
        }
    ];

    for (let i = 0; i < searchStrategies.length; i++) {
        try {
            userElement = searchStrategies[i]();
            if (userElement) {
                console.log(`🔍 DEBUG: Найден userElement стратегией ${i + 1}:`, userElement.outerHTML.substring(0, 300));
                break;
            }
        } catch (e) {
            console.log(`🔍 DEBUG: Стратегия ${i + 1} не сработала:`, e.message);
        }
    }

    if (!userElement) {
        console.error("🚨 КРИТИЧЕСКАЯ ОШИБКА: НЕ НАЙДЕН РОДИТЕЛЬСКИЙ ЭЛЕМЕНТ ПОЛЬЗОВАТЕЛЯ!");
        console.log("🔍 DEBUG: Полная структура кнопки:", button.outerHTML);
        console.log("🔍 DEBUG: Родительские элементы:");
        let current = button.parentElement;
        for (let i = 0; i < 10 && current; i++) {
            console.log(`  Уровень ${i + 1}:`, current.outerHTML.substring(0, 200));
            current = current.parentElement;
        }
    }

    let username = null;
    let foundUsernames = []; // Массив всех найденных имен для отладки

    if (userElement) {
        // МНОЖЕСТВЕННЫЕ СПОСОБЫ ПОИСКА ССЫЛКИ НА ПРОФИЛЬ
        const linkSearchStrategies = [
            () => userElement.querySelector(CONFIG.USER_PROFILE_LINK_SELECTOR),
            () => userElement.querySelector('a[href*="instagram.com/"]'),
            () => userElement.querySelector('a[href^="/"]'),
            () => userElement.querySelector('a[href*="/"]'),
            () => userElement.querySelector('a'),
            () => Array.from(userElement.querySelectorAll('a')).find(a => a.href && a.href.includes('/')),
            () => Array.from(userElement.querySelectorAll('*')).find(el => el.textContent && el.textContent.match(/^[a-zA-Z0-9._]+$/))
        ];

        let userLinkElement = null;
        for (let i = 0; i < linkSearchStrategies.length; i++) {
            try {
                userLinkElement = linkSearchStrategies[i]();
                if (userLinkElement) {
                    console.log(`🔍 DEBUG: Найдена ссылка стратегией ${i + 1}:`, userLinkElement.outerHTML?.substring(0, 200) || userLinkElement.textContent);
                    break;
                }
            } catch (e) {
                console.log(`🔍 DEBUG: Стратегия поиска ссылки ${i + 1} не сработала:`, e.message);
            }
        }

        // ИЗВЛЕЧЕНИЕ ИМЕНИ ПОЛЬЗОВАТЕЛЯ ИЗ URL
        if (userLinkElement && userLinkElement.href) {
            console.log("🔍 DEBUG: HREF ссылки на профиль:", userLinkElement.href);

            // Множественные регулярные выражения для извлечения имени
            const regexStrategies = [
                CONFIG.USERNAME_IN_PROFILE_LINK_REGEX,
                /\/([^\/\?#]+)\/?$/,
                /instagram\.com\/([^\/\?#]+)/,
                /\/([a-zA-Z0-9._]+)\/?/,
                /([a-zA-Z0-9._]+)/
            ];

            for (let i = 0; i < regexStrategies.length; i++) {
                const match = userLinkElement.href.match(regexStrategies[i]);
                if (match && match[1] && match[1].length > 0) {
                    const extractedName = match[1];
                    foundUsernames.push(`regex${i + 1}: ${extractedName}`);
                    if (!username && extractedName.length > 0 && !extractedName.includes('explore') && !extractedName.includes('direct')) {
                        username = extractedName;
                        console.log(`🔍 DEBUG: Извлечено имя пользователя regex ${i + 1}:`, username);
                    }
                }
            }
        }

        // ИЗВЛЕЧЕНИЕ ИМЕНИ ИЗ ТЕКСТА ЭЛЕМЕНТОВ
        if (!username && userElement) {
            const textElements = Array.from(userElement.querySelectorAll('*')).filter(el =>
                el.textContent &&
                el.textContent.trim().length > 0 &&
                el.textContent.trim().length < 50 &&
                !el.querySelector('*') // Только листовые элементы
            );

            console.log("🔍 DEBUG: Найденные текстовые элементы:", textElements.map(el => el.textContent.trim()));

            for (const el of textElements) {
                const text = el.textContent.trim().replace('@', '');
                if (text.match(/^[a-zA-Z0-9._]+$/) && text.length > 2) {
                    foundUsernames.push(`text: ${text}`);
                    if (!username) {
                        username = text;
                        console.log("🔍 DEBUG: Извлечено имя из текста:", username);
                    }
                }
            }
        }

        console.log("🔍 DEBUG: ВСЕ НАЙДЕННЫЕ ИМЕНА:", foundUsernames);
    }

    // КРИТИЧЕСКАЯ ПРОВЕРКА ИСКЛЮЧЕНИЙ
    console.log("🔍 DEBUG: Финальное имя пользователя для проверки:", username);
    console.log("🔍 DEBUG: Список исключенных пользователей:", JSON.stringify(CONFIG.EXCLUDED_USERS));

    if (username) {
        const isExcluded = CONFIG.EXCLUDED_USERS.includes(username);
        console.log(`🔍 DEBUG: Пользователь '${username}' ${isExcluded ? '🚨 ЕСТЬ' : '✅ НЕТ'} в списке исключений.`);

        if (isExcluded) {
            console.error(`🚨🚨🚨 КРИТИЧЕСКОЕ ПРЕДУПРЕЖДЕНИЕ: ПОЛЬЗОВАТЕЛЬ '${username}' В СПИСКЕ ИСКЛЮЧЕНИЙ! ОТПИСКА ЗАБЛОКИРОВАНА! 🚨🚨🚨`);
            button.dataset.processed = "true";
            return 'skipped';
        }
    } else {
        console.error("🚨 КРИТИЧЕСКАЯ ОШИБКА: НЕ УДАЛОСЬ ИЗВЛЕЧЬ ИМЯ ПОЛЬЗОВАТЕЛЯ!");
        console.log("🔍 DEBUG: Все попытки извлечения имени:", foundUsernames);

        // ПРИНУДИТЕЛЬНАЯ ОСТАНОВКА если не можем определить пользователя
        console.error("🚨 ОСТАНОВКА СКРИПТА: Невозможно определить пользователя для проверки исключений!");
        button.dataset.processed = "true";
        return false; // Останавливаем процесс
    }

    console.log("✅ DEBUG: Проверка исключений пройдена. Пользователь НЕ в списке исключений. Продолжаем отписку.");
    // --- КОНЕЦ КРИТИЧЕСКОЙ ПРОВЕРКИ --- //

    // ВАЖНО: Помечаем кнопку как обработанную ТОЛЬКО ПОСЛЕ проверки исключений
    button.dataset.processed = "true";

    // ДОПОЛНИТЕЛЬНАЯ ЗАЩИТА: Повторная проверка перед кликом
    if (username && CONFIG.EXCLUDED_USERS.includes(username)) {
        console.error(`🚨🚨🚨 ДВОЙНАЯ ЗАЩИТА СРАБОТАЛА! Пользователь '${username}' в списке исключений! Клик заблокирован! 🚨🚨🚨`);
        return 'skipped';
    }

    await sleep(getRandomDelay()); // Используем значения по умолчанию из CONFIG
    console.log(`✅ ${CONFIG.LOG_CLICKING_UNFOLLOW} для пользователя: ${username || 'НЕИЗВЕСТНЫЙ'}`);

    // ДИАГНОСТИКА ПЕРЕД КЛИКОМ
    console.log("🔍 ДИАГНОСТИКА ПЕРЕД КЛИКОМ:");
    console.log("  - Кнопка существует:", !!button);
    console.log("  - Кнопка видима:", button.offsetParent !== null);
    console.log("  - Кнопка активна:", !button.disabled);
    console.log("  - Текст кнопки:", button.textContent.trim());

    try {
        button.click();
        console.log("✅ Клик по кнопке 'Отписаться' выполнен успешно");
    } catch (error) {
        console.error("❌ ОШИБКА при клике по кнопке 'Отписаться':", error);
        return false;
    }

    console.log("Ожидаем после клика на 'Отписаться':", CONFIG.UNFOLLOW_CLICK_DELAY, "мс");
    await sleep(CONFIG.UNFOLLOW_CLICK_DELAY);

    console.log("🔍 Ищем кнопку подтверждения по тексту во всех активных диалоговых окнах...");
    let confirmButton = null;
    let actualConfirmationDialog = null; // Диалог, в котором найдена кнопка подтверждения

    // РАСШИРЕННАЯ ДИАГНОСТИКА ПОИСКА ДИАЛОГОВ
    const allDialogs = Array.from(document.querySelectorAll('div[role="dialog"]'));
    console.log(`🔍 Найдено ${allDialogs.length} диалоговых окон для поиска кнопки подтверждения.`);

    if (allDialogs.length === 0) {
        console.error("❌ КРИТИЧЕСКАЯ ОШИБКА: Диалоговые окна не найдены после клика!");
        console.log("💡 Возможные причины:");
        console.log("   - Клик по кнопке 'Подписки' не сработал");
        console.log("   - Instagram изменил структуру диалогов");
        console.log("   - Блокировка запросов браузером");
        return false;
    }

    for (const currentDialog of allDialogs) {
        if (currentDialog.offsetParent === null) {
            console.log(`Пропускаем невидимый диалог: ${currentDialog.outerHTML.substring(0,100)}...`);
            continue; // Skip non-visible dialogs
        }

        const potentialConfirmButtons = Array.from(currentDialog.querySelectorAll('button'));
        console.log(`В диалоге ${currentDialog.outerHTML.substring(0,100)}... найдено ${potentialConfirmButtons.length} потенциальных кнопок подтверждения.`);
        potentialConfirmButtons.forEach((btn, idx) => {
            console.log(`  Кнопка ${idx} в этом диалоге: textContent="${btn.textContent.trim()}", outerHTML="${btn.outerHTML.substring(0,100)}..."`);
        });

        // Улучшенный поиск кнопки подтверждения с множественными вариантами текста
        confirmButton = potentialConfirmButtons.find(btn => {
            const buttonText = btn.textContent.trim();
            console.log(`🔍 Проверяем кнопку подтверждения: "${buttonText}"`);

            // Проверяем точное совпадение с любым из вариантов
            const exactMatch = CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.includes(buttonText);
            if (exactMatch) {
                console.log(`✅ Найдено точное совпадение: "${buttonText}"`);
                return true;
            }

            // Проверяем частичное совпадение (содержит ключевые слова)
            const partialMatch = CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.some(alt =>
                buttonText.includes(alt) || alt.includes(buttonText)
            );
            if (partialMatch) {
                console.log(`✅ Найдено частичное совпадение: "${buttonText}"`);
                return true;
            }

            // Дополнительная проверка на ключевые слова
            const keywords = ['отмен', 'unfollow', 'отпис', 'перестать', 'читать'];
            const keywordMatch = keywords.some(keyword =>
                buttonText.toLowerCase().includes(keyword.toLowerCase())
            );
            if (keywordMatch) {
                console.log(`✅ Найдено совпадение по ключевому слову: "${buttonText}"`);
                return true;
            }

            return false;
        });

        if (confirmButton) {
            actualConfirmationDialog = currentDialog;
            console.log("Кнопка подтверждения найдена в диалоге:", actualConfirmationDialog, confirmButton);
            break; // Нашли кнопку, выходим из цикла по диалогам
        }
    }

    if (confirmButton && actualConfirmationDialog) {
        // ТРОЙНАЯ ЗАЩИТА: Проверка перед подтверждением отписки
        if (username && CONFIG.EXCLUDED_USERS.includes(username)) {
            console.error(`🚨🚨🚨 ТРОЙНАЯ ЗАЩИТА СРАБОТАЛА! Пользователь '${username}' в списке исключений! Подтверждение отписки заблокировано! 🚨🚨🚨`);
            return 'skipped';
        }

        console.log("Ожидаем перед кликом на кнопку подтверждения:", `min: ${CONFIG.CONFIRM_MIN_DELAY}, max: ${CONFIG.CONFIRM_MAX_DELAY}`);
        await sleep(getRandomDelay(CONFIG.CONFIRM_MIN_DELAY, CONFIG.CONFIRM_MAX_DELAY));

        // ДИАГНОСТИКА ПЕРЕД КЛИКОМ ПО КНОПКЕ ПОДТВЕРЖДЕНИЯ
        console.log("🔍 ДИАГНОСТИКА КНОПКИ ПОДТВЕРЖДЕНИЯ:");
        console.log("  - Кнопка существует:", !!confirmButton);
        console.log("  - Кнопка видима:", confirmButton.offsetParent !== null);
        console.log("  - Кнопка активна:", !confirmButton.disabled);
        console.log("  - Текст кнопки:", confirmButton.textContent.trim());
        console.log("  - Диалог видим:", actualConfirmationDialog.offsetParent !== null);

        console.log(`✅ ${CONFIG.LOG_CLICKING_CONFIRM_BUTTON} для пользователя: ${username || 'НЕИЗВЕСТНЫЙ'}`);

        try {
            confirmButton.click();
            console.log("✅ Клик по кнопке подтверждения выполнен успешно");
        } catch (error) {
            console.error("❌ ОШИБКА при клике по кнопке подтверждения:", error);
            return false;
        }

        console.log("Ожидаем после клика на кнопку подтверждения:", CONFIG.POST_UNFOLLOW_DELAY, "мс");
        await sleep(CONFIG.POST_UNFOLLOW_DELAY);

        console.log("Проверяем исчезновение кнопки подтверждения в диалоге:", actualConfirmationDialog);
        for (let i = 0; i < CONFIG.CONFIRM_CHECK_RETRIES; i++) {
            let stillExists = null;
            // Перепроверяем, видим ли мы все еще тот же диалог подтверждения
            if (actualConfirmationDialog && actualConfirmationDialog.offsetParent !== null) {
                const potentialConfirmButtonsAfterClick = Array.from(actualConfirmationDialog.querySelectorAll('button'));
                stillExists = potentialConfirmButtonsAfterClick.find(btn => {
                    const buttonText = btn.textContent.trim();

                    // Используем ту же логику поиска, что и при первоначальном поиске
                    const exactMatch = CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.includes(buttonText);
                    const partialMatch = CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.some(alt =>
                        buttonText.includes(alt) || alt.includes(buttonText)
                    );
                    const keywords = ['отмен', 'unfollow', 'отпис', 'перестать', 'читать'];
                    const keywordMatch = keywords.some(keyword =>
                        buttonText.toLowerCase().includes(keyword.toLowerCase())
                    );

                    return exactMatch || partialMatch || keywordMatch;
                });
            } else if (!actualConfirmationDialog) {
                 console.log("Диалог подтверждения (actualConfirmationDialog) не был определен.");
            } else {
                console.log("Диалог подтверждения (actualConfirmationDialog) больше не видим или не существует.");
                // Если диалог исчез, считаем это успехом, так как кнопка в нем точно исчезла
                console.log(CONFIG.LOG_UNFOLLOW_SUCCESSFUL, "(диалог подтверждения исчез)");
                return true; // УСПЕХ
            }

            if (!stillExists) {
                console.log(CONFIG.LOG_UNFOLLOW_SUCCESSFUL, "(кнопка подтверждения исчезла из диалога)");
                return true; // УСПЕХ
            }
            console.log(`${CONFIG.LOG_WAITING_UNFOLLOW_COMPLETION_ATTEMPT} ${i + 1}. Кнопка подтверждения все еще существует в диалоге:`, stillExists);
            await sleep(CONFIG.MODAL_OPEN_RETRY_DELAY);
        }
        // Если цикл завершился, кнопка не исчезла.
    }

    let reasonForFailure = "";
    if (!actualConfirmationDialog && !confirmButton) {
        reasonForFailure = `кнопка подтверждения не найдена ни в одном видимом диалоговом окне. Искали варианты: ${CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES.join(', ')}`;

        console.error("🚨 ДИАГНОСТИКА: Кнопка подтверждения не найдена!");
        console.log("🔍 Искали следующие варианты текста:", CONFIG.CONFIRM_UNFOLLOW_ALTERNATIVES);

        const allButtonsOnPage = Array.from(document.querySelectorAll('button'));
        console.log(`🔍 Всего кнопок на странице: ${allButtonsOnPage.length}`);

        // Показываем только кнопки, которые могут быть кнопками подтверждения
        const suspiciousButtons = allButtonsOnPage.filter(btn => {
            const text = btn.textContent.trim().toLowerCase();
            return text.length > 0 && text.length < 50 && (
                text.includes('отмен') ||
                text.includes('unfollow') ||
                text.includes('отпис') ||
                text.includes('перестать') ||
                text.includes('читать') ||
                text.includes('подпис') ||
                text.includes('follow')
            );
        });

        console.log(`🔍 Подозрительные кнопки (могут быть кнопками подтверждения): ${suspiciousButtons.length}`);
        suspiciousButtons.forEach((btn, index) => {
            console.log(`🔘 Подозрительная кнопка ${index + 1}: textContent="${btn.textContent.trim()}", visible=${btn.offsetParent !== null}`);
        });

        // Показываем все кнопки только если их мало
        if (allButtonsOnPage.length <= 20) {
            console.log("🔍 Все кнопки на странице:");
            allButtonsOnPage.forEach((btn, index) => {
                console.log(`🔘 Кнопка ${index + 1}: textContent="${btn.textContent.trim()}", visible=${btn.offsetParent !== null}`);
            });
        }
    } else if (actualConfirmationDialog && !confirmButton) {
        // Эта ситуация маловероятна с текущей логикой, но для полноты
        reasonForFailure = `в диалоге ${actualConfirmationDialog.outerHTML.substring(0,100)}... не найдена кнопка подтверждения.`;
    } else { // Кнопка была найдена, но не исчезла (или actualConfirmationDialog стал null)
        reasonForFailure = "кнопка подтверждения не исчезла после клика и проверки, или диалог подтверждения исчез неожиданно.";
    }
    
    console.error(`${CONFIG.LOG_UNFOLLOW_FAILED}: ${reasonForFailure}. Это может быть связано с проблемами сети или блокировкой запросов (например, ERR_BLOCKED_BY_CLIENT). Убедитесь, что расширения браузера не блокируют запросы к Instagram.`);
    return false;
}

// Функция для закрытия модального окна
async function closeModalWindow() {
    console.log(CONFIG.LOG_SEARCHING_CLOSE_MODAL_BUTTON);
    let closeButton = document.querySelector(CONFIG.CLOSE_MODAL_BUTTON_SELECTOR);
    if (!closeButton) {
        console.error(CONFIG.LOG_SVG_CLOSE_BUTTON_NOT_FOUND);
        return false;
    }

    // Ищем ближайший кликабельный элемент
    while (closeButton && typeof closeButton.click !== 'function') {
        closeButton = closeButton.parentElement;
        if (!closeButton || closeButton.tagName === 'BODY') {
            console.error(CONFIG.LOG_CLICKABLE_PARENT_NOT_FOUND);
            return false;
        }
    }

    console.log(CONFIG.LOG_CLOSE_BUTTON_FOUND, closeButton);
    closeButton.click();
    await sleep(CONFIG.MODAL_CLOSE_DELAY);

    // Проверяем закрытие
    for (let i = 0; i < CONFIG.MODAL_CLOSE_CHECK_RETRIES; i++) {
        if (!document.querySelector(CONFIG.MODAL_DIALOG_SELECTOR)) {
            console.log(CONFIG.LOG_MODAL_SUCCESSFULLY_CLOSED);
            return true;
        }
        console.log(`${CONFIG.LOG_WAITING_MODAL_CLOSE_ATTEMPT} ${i + 1}`);
        await sleep(CONFIG.MODAL_OPEN_RETRY_DELAY); // Используем общую задержку для попыток
    }

    console.error(CONFIG.LOG_MODAL_NOT_CLOSED);
    return false;
}

// Основная функция управления процессом отписки
async function processUnfollows(maxUnfollowsFromPrompt) {
    let unfollowed = 0;

    console.log(CONFIG.LOG_ASKING_UNFOLLOW_COUNT);
    const max = parseInt(maxUnfollowsFromPrompt) || CONFIG.DEFAULT_MAX_UNFOLLOWS;
    const isUnlimited = max === 0;
    if (isUnlimited) {
        console.log(`${CONFIG.LOG_STARTING_UNFOLLOWS_MAX} неограниченно.`);
    } else {
        console.log(`${CONFIG.LOG_STARTING_UNFOLLOWS_MAX} ${max} ${CONFIG.LOG_UNFOLLOWS_SUFFIX}`);
    }

    console.log(CONFIG.LOG_CHECKING_AND_OPENING_MODAL);
    const modalOpened = await openModalWindow();
    if (!modalOpened) {
        console.error(CONFIG.LOG_FAILED_TO_OPEN_MODAL_EXITING);
        return;
    }
    console.log(CONFIG.LOG_MODAL_ACTIVE_CONTINUING);

    let scrollableModalContent = null;
    for (let i = 0; i < 5; i++) { // Увеличено количество попыток до 5
        scrollableModalContent = document.querySelector(CONFIG.SCROLLABLE_MODAL_CONTENT_SELECTOR_1) ||
                       document.querySelector(CONFIG.SCROLLABLE_MODAL_CONTENT_SELECTOR_2) ||
                       document.querySelector(CONFIG.SCROLLABLE_MODAL_CONTENT_SELECTOR_3);
        if (scrollableModalContent) break;
        console.log(`${CONFIG.LOG_ATTEMPT_FIND_SCROLL_ELEMENT} ${i + 1} ${CONFIG.LOG_FIND_SCROLL_ELEMENT_SUFFIX}`);
        await sleep(CONFIG.MODAL_OPEN_RETRY_DELAY);
    }

    if (!scrollableModalContent) {
        console.error(`${CONFIG.LOG_SCROLL_ELEMENT_NOT_FOUND_AFTER_ATTEMPTS} 5 ${CONFIG.LOG_SCROLL_ELEMENT_NOT_FOUND_AFTER_ATTEMPTS_SUFFIX}`);
        await closeModalWindow(); // Пытаемся закрыть окно, если оно было открыто
        return;
    }
    console.log(CONFIG.LOG_FOUND_SCROLL_ELEMENT, scrollableModalContent);

    while (isUnlimited || unfollowed < max) {
        const unfollowResult = await performUnfollow(scrollableModalContent); // Передаем найденный элемент

        if (unfollowResult === 'skipped') {
            console.log(CONFIG.LOG_USER_SKIPPED_EXCLUSION);
            // Пользователь пропущен, счетчик не увеличиваем. Кнопка уже помечена как обработанная в performUnfollow.
        } else if (unfollowResult === true) {
            unfollowed++;
            if (isUnlimited) {
                console.log(`${CONFIG.LOG_UNFOLLOW_COMPLETED_NUMBER}${unfollowed}.`);
            } else {
                console.log(`${CONFIG.LOG_UNFOLLOW_COMPLETED_NUMBER}${unfollowed} ${CONFIG.LOG_UNFOLLOW_COMPLETED_OUT_OF} ${max}.`);
            }
        } else { // unfollowResult === false
            // Причина неудачи (нет кнопок или ошибка подтверждения) уже залогирована в performUnfollow.
            console.log("⚠️ Не удалось найти необработанные кнопки. Попробуем прокрутить для загрузки новых пользователей...");

            // Попытка дополнительной прокрутки для загрузки новых пользователей
            await safeScroll(scrollableModalContent);
            await sleep(2000); // Даем время на загрузку новых элементов

            // Проверяем, появились ли новые кнопки после прокрутки
            const newButtons = Array.from(scrollableModalContent.querySelectorAll('button'))
                .filter(btn => (btn.textContent.includes(CONFIG.UNFOLLOW_BUTTON_TEXT_RU) || btn.textContent.includes(CONFIG.UNFOLLOW_BUTTON_TEXT_EN)) && !btn.dataset.processed);

            if (newButtons.length > 0) {
                console.log(`✅ После прокрутки найдено ${newButtons.length} новых необработанных кнопок. Продолжаем...`);
                continue; // Продолжаем цикл с новыми кнопками
            } else {
                console.log("❌ После прокрутки новые кнопки не появились. Завершение цикла отписок.");
                break;
            }
        }

        // Прокрутка и задержка выполняются, если цикл не был прерван (т.е. unfollowResult был true или 'skipped')
        console.log("[DEBUG] Перед вызовом safeScroll. scrollableModalContent:", scrollableModalContent);
        console.log("[DEBUG] Тип scrollableModalContent:", typeof scrollableModalContent);
        if (scrollableModalContent && typeof scrollableModalContent.getAttribute === 'function') {
            console.log("[DEBUG] outerHTML scrollableModalContent (начало):", scrollableModalContent.outerHTML.substring(0, 200));
        } else if (scrollableModalContent) {
            console.log("[DEBUG] scrollableModalContent существует, но не является DOM элементом с getAttribute или outerHTML.");
        } else {
            console.log("[DEBUG] scrollableModalContent НЕ существует (null или undefined) перед вызовом safeScroll.");
        }
        await safeScroll(scrollableModalContent);

        const delay = getRandomDelay(CONFIG.MIN_RANDOM_DELAY * 8, CONFIG.MAX_RANDOM_DELAY * 13); // Увеличены и изменены множители для большей случайности и задержки
        console.log(`${CONFIG.LOG_WAITING_BEFORE_NEXT_UNFOLLOW} ${delay / 1000} ${CONFIG.LOG_SECONDS_BEFORE_NEXT_UNFOLLOW}`);
        await sleep(delay);
    }

    console.log(`${CONFIG.LOG_DONE_UNFOLLOWED} ${unfollowed}`);
    await closeModalWindow();
}

// Запуск скрипта с выбором количества отписок
(async () => {
    try {
        const maxUnfollowsFromPrompt = prompt(CONFIG.PROMPT_MESSAGE_UNLIMITED, String(CONFIG.DEFAULT_MAX_UNFOLLOWS));
        console.log(`${CONFIG.LOG_STARTING_PROCESS_MAX_UNFOLLOWS} ${maxUnfollowsFromPrompt === "0" ? "неограниченно" : maxUnfollowsFromPrompt} ${CONFIG.LOG_UNFOLLOWS_SUFFIX}`);
        await processUnfollows(maxUnfollowsFromPrompt);
        console.log(CONFIG.LOG_UNFOLLOW_PROCESS_AND_CLOSE_MODAL_COMPLETE);
    } catch (error) {
        console.error(CONFIG.LOG_ERROR_OCCURRED, error);
    }
})();