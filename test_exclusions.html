<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест системы исключений</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Тест системы исключений для скрипта отписки Instagram</h1>
        
        <div class="test-section">
            <h2>Описание исправлений</h2>
            <div class="info">
                <h3>Основные проблемы, которые были исправлены:</h3>
                <ul>
                    <li><strong>Критическая ошибка:</strong> Кнопка помечалась как обработанная ДО проверки исключений</li>
                    <li><strong>Улучшенные селекторы:</strong> Добавлены дополнительные способы поиска элементов пользователя</li>
                    <li><strong>Улучшенное регулярное выражение:</strong> Более надежное извлечение имени пользователя из URL</li>
                    <li><strong>Дополнительный способ:</strong> Извлечение имени пользователя из текста ссылки</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>Тест регулярного выражения</h2>
            <button onclick="testRegex()">Запустить тест регулярного выражения</button>
            <div id="regexResults"></div>
        </div>

        <div class="test-section">
            <h2>Тест проверки исключений</h2>
            <button onclick="testExclusions()">Запустить тест исключений</button>
            <div id="exclusionResults"></div>
        </div>

        <div class="test-section">
            <h2>Симуляция работы скрипта</h2>
            <button onclick="simulateScript()">Симулировать работу скрипта</button>
            <div id="simulationResults"></div>
        </div>
    </div>

    <script>
        // Конфигурация из исправленного скрипта
        const CONFIG = {
            USERNAME_IN_PROFILE_LINK_REGEX: /\/([^\/\?#]+)\/?(?:\?.*)?(?:#.*)?$/,
            EXCLUDED_USERS: ["e.pavlyuk_", "esipenkoyana93", "julia_figulia", "irok_nu_ti_chego", "pa.vel6627rus"]
        };

        function testRegex() {
            const testUrls = [
                "https://www.instagram.com/e.pavlyuk_/",
                "https://www.instagram.com/esipenkoyana93",
                "https://www.instagram.com/julia_figulia/?hl=en",
                "https://www.instagram.com/irok_nu_ti_chego#posts",
                "https://www.instagram.com/pa.vel6627rus/?utm_source=ig_web_button_share_sheet",
                "/e.pavlyuk_/",
                "/esipenkoyana93",
                "/julia_figulia/",
                "/some_random_user/",
                "https://www.instagram.com/explore/",
                "https://www.instagram.com/direct/"
            ];

            let results = '<h3>Результаты тестирования регулярного выражения:</h3>';
            
            testUrls.forEach(url => {
                const match = url.match(CONFIG.USERNAME_IN_PROFILE_LINK_REGEX);
                const username = match && match[1] ? match[1] : null;
                const isExcluded = username ? CONFIG.EXCLUDED_USERS.includes(username) : false;
                
                const resultClass = username ? (isExcluded ? 'error' : 'success') : 'info';
                results += `<div class="test-result ${resultClass}">
                    <strong>URL:</strong> ${url}<br>
                    <strong>Извлеченное имя:</strong> ${username || 'НЕ НАЙДЕНО'}<br>
                    <strong>В списке исключений:</strong> ${isExcluded ? 'ДА' : 'НЕТ'}
                </div>`;
            });

            document.getElementById('regexResults').innerHTML = results;
        }

        function testExclusions() {
            const testUsers = [
                "e.pavlyuk_",
                "esipenkoyana93", 
                "julia_figulia",
                "irok_nu_ti_chego",
                "pa.vel6627rus",
                "random_user_1",
                "another_user",
                "test_user_123"
            ];

            let results = '<h3>Результаты тестирования системы исключений:</h3>';
            
            testUsers.forEach(username => {
                const isExcluded = CONFIG.EXCLUDED_USERS.includes(username);
                const action = isExcluded ? 'ПРОПУСТИТЬ' : 'ОТПИСАТЬСЯ';
                const resultClass = isExcluded ? 'error' : 'success';
                
                results += `<div class="test-result ${resultClass}">
                    <strong>Пользователь:</strong> ${username}<br>
                    <strong>Действие:</strong> ${action}
                </div>`;
            });

            document.getElementById('exclusionResults').innerHTML = results;
        }

        function simulateScript() {
            // Симуляция работы исправленного скрипта
            const mockUsers = [
                { username: "e.pavlyuk_", shouldSkip: true },
                { username: "random_user_1", shouldSkip: false },
                { username: "esipenkoyana93", shouldSkip: true },
                { username: "another_user", shouldSkip: false },
                { username: "julia_figulia", shouldSkip: true }
            ];

            let results = '<h3>Симуляция работы исправленного скрипта:</h3>';
            let processedCount = 0;
            let skippedCount = 0;
            let unfollowedCount = 0;

            mockUsers.forEach((user, index) => {
                const isExcluded = CONFIG.EXCLUDED_USERS.includes(user.username);
                
                if (isExcluded) {
                    skippedCount++;
                    results += `<div class="test-result error">
                        <strong>Шаг ${index + 1}:</strong> Пользователь "${user.username}" ПРОПУЩЕН (в списке исключений)
                    </div>`;
                } else {
                    unfollowedCount++;
                    results += `<div class="test-result success">
                        <strong>Шаг ${index + 1}:</strong> Пользователь "${user.username}" ОТПИСАН
                    </div>`;
                }
                processedCount++;
            });

            results += `<div class="test-result info">
                <strong>Итоги симуляции:</strong><br>
                Всего обработано: ${processedCount}<br>
                Пропущено (исключения): ${skippedCount}<br>
                Отписано: ${unfollowedCount}
            </div>`;

            document.getElementById('simulationResults').innerHTML = results;
        }
    </script>
</body>
</html>
