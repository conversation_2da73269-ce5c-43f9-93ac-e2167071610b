// ОТЛАДОЧНАЯ ВЕРСИЯ СКРИПТА ДЛЯ ДИАГНОСТИКИ ПРОБЛЕМ С ИСКЛЮЧЕНИЯМИ
// Запустите этот скрипт в консоли браузера на странице Instagram для диагностики

console.log("🔧 ЗАПУСК ОТЛАДОЧНОГО СКРИПТА ДЛЯ ДИАГНОСТИКИ ИСКЛЮЧЕНИЙ");

const DEBUG_CONFIG = {
    EXCLUDED_USERS: ["e.pavlyuk_", "esipenkoyana93", "julia_figulia", "irok_nu_ti_chego", "pa.vel6627rus"],
    USERNAME_IN_PROFILE_LINK_REGEX: /\/([^\/\?#]+)\/?(?:\?.*)?(?:#.*)?$/
};

// Функция для анализа DOM структуры модального окна
function analyzeModalStructure() {
    console.log("🔍 АНАЛИЗ СТРУКТУРЫ МОДАЛЬНОГО ОКНА");
    
    const modal = document.querySelector('div[role="dialog"]');
    if (!modal) {
        console.error("❌ Модальное окно не найдено!");
        return;
    }
    
    console.log("✅ Модальное окно найдено:", modal);
    
    // Поиск всех кнопок в модальном окне
    const allButtons = Array.from(modal.querySelectorAll('button'));
    console.log(`📊 Всего кнопок в модальном окне: ${allButtons.length}`);
    
    allButtons.forEach((btn, index) => {
        console.log(`🔘 Кнопка ${index + 1}:`, {
            text: btn.textContent.trim(),
            classes: btn.className,
            html: btn.outerHTML.substring(0, 150) + "..."
        });
    });
    
    // Поиск кнопок "Подписки"
    const followButtons = allButtons.filter(btn => 
        btn.textContent.includes("Подписки") || 
        btn.textContent.includes("Following")
    );
    
    console.log(`🎯 Найдено кнопок "Подписки": ${followButtons.length}`);
    
    return { modal, allButtons, followButtons };
}

// Функция для анализа структуры строки пользователя
function analyzeUserRowStructure(button) {
    console.log("🔍 АНАЛИЗ СТРУКТУРЫ СТРОКИ ПОЛЬЗОВАТЕЛЯ");
    console.log("🔘 Анализируемая кнопка:", button.outerHTML.substring(0, 200));
    
    // Попробуем все возможные способы найти родительский элемент
    const strategies = [
        { name: "closest div with padding", fn: () => button.closest('div[style*="padding"]') },
        { name: "closest listitem", fn: () => button.closest('[role="listitem"]') },
        { name: "closest div twice", fn: () => button.closest('div').closest('div') },
        { name: "parentElement x2", fn: () => button.parentElement?.parentElement },
        { name: "parentElement x3", fn: () => button.parentElement?.parentElement?.parentElement },
        { name: "closest div with class x", fn: () => button.closest('div[class*="x"]') },
        { name: "search up for links", fn: () => {
            let current = button.parentElement;
            for (let i = 0; i < 5 && current; i++) {
                if (current.querySelector('a[href*="/"]')) return current;
                current = current.parentElement;
            }
            return null;
        }}
    ];
    
    const results = [];
    strategies.forEach(strategy => {
        try {
            const result = strategy.fn();
            results.push({
                strategy: strategy.name,
                found: !!result,
                element: result,
                links: result ? Array.from(result.querySelectorAll('a')).length : 0
            });
            
            if (result) {
                console.log(`✅ ${strategy.name}:`, {
                    html: result.outerHTML.substring(0, 300) + "...",
                    links: Array.from(result.querySelectorAll('a')).map(a => ({
                        href: a.href,
                        text: a.textContent.trim()
                    }))
                });
            } else {
                console.log(`❌ ${strategy.name}: не найден`);
            }
        } catch (e) {
            console.log(`⚠️ ${strategy.name}: ошибка -`, e.message);
        }
    });
    
    return results;
}

// Функция для извлечения имени пользователя из элемента
function extractUsernameFromElement(userElement) {
    console.log("🔍 ИЗВЛЕЧЕНИЕ ИМЕНИ ПОЛЬЗОВАТЕЛЯ");
    
    if (!userElement) {
        console.error("❌ Элемент пользователя не передан");
        return null;
    }
    
    const foundUsernames = [];
    
    // Поиск ссылок
    const links = Array.from(userElement.querySelectorAll('a'));
    console.log(`🔗 Найдено ссылок: ${links.length}`);
    
    links.forEach((link, index) => {
        console.log(`🔗 Ссылка ${index + 1}:`, {
            href: link.href,
            text: link.textContent.trim(),
            html: link.outerHTML.substring(0, 200) + "..."
        });
        
        if (link.href) {
            // Попробуем разные регулярные выражения
            const regexes = [
                { name: "main regex", regex: DEBUG_CONFIG.USERNAME_IN_PROFILE_LINK_REGEX },
                { name: "simple regex", regex: /\/([^\/\?#]+)\/?$/ },
                { name: "instagram.com regex", regex: /instagram\.com\/([^\/\?#]+)/ },
                { name: "any username regex", regex: /\/([a-zA-Z0-9._]+)\/?/ }
            ];
            
            regexes.forEach(r => {
                const match = link.href.match(r.regex);
                if (match && match[1]) {
                    foundUsernames.push({
                        source: `link ${index + 1} - ${r.name}`,
                        username: match[1],
                        href: link.href
                    });
                }
            });
        }
        
        // Извлечение из текста ссылки
        const linkText = link.textContent.trim().replace('@', '');
        if (linkText.match(/^[a-zA-Z0-9._]+$/) && linkText.length > 2) {
            foundUsernames.push({
                source: `link ${index + 1} text`,
                username: linkText,
                href: link.href
            });
        }
    });
    
    // Поиск в текстовых элементах
    const textElements = Array.from(userElement.querySelectorAll('*')).filter(el => 
        el.textContent && 
        el.textContent.trim().length > 0 && 
        el.textContent.trim().length < 50 &&
        !el.querySelector('*')
    );
    
    console.log(`📝 Найдено текстовых элементов: ${textElements.length}`);
    textElements.forEach((el, index) => {
        const text = el.textContent.trim().replace('@', '');
        console.log(`📝 Текст ${index + 1}: "${text}"`);
        
        if (text.match(/^[a-zA-Z0-9._]+$/) && text.length > 2) {
            foundUsernames.push({
                source: `text element ${index + 1}`,
                username: text,
                element: el.outerHTML.substring(0, 100)
            });
        }
    });
    
    console.log("🎯 ВСЕ НАЙДЕННЫЕ ИМЕНА ПОЛЬЗОВАТЕЛЕЙ:", foundUsernames);
    
    // Выбираем лучший вариант
    const bestUsername = foundUsernames.find(u => 
        !u.username.includes('explore') && 
        !u.username.includes('direct') &&
        !u.username.includes('accounts')
    );
    
    return bestUsername ? bestUsername.username : null;
}

// Функция для проверки исключений
function checkExclusions(username) {
    console.log("🔍 ПРОВЕРКА ИСКЛЮЧЕНИЙ");
    console.log("👤 Проверяемый пользователь:", username);
    console.log("📋 Список исключений:", DEBUG_CONFIG.EXCLUDED_USERS);
    
    if (!username) {
        console.error("❌ Имя пользователя не определено!");
        return { excluded: false, reason: "username is null" };
    }
    
    const isExcluded = DEBUG_CONFIG.EXCLUDED_USERS.includes(username);
    console.log(`🎯 Результат: ${isExcluded ? '🚨 ИСКЛЮЧЕН' : '✅ НЕ ИСКЛЮЧЕН'}`);
    
    return { excluded: isExcluded, reason: isExcluded ? "in exclusion list" : "not in exclusion list" };
}

// Основная функция диагностики
function runDiagnostics() {
    console.log("🚀 ЗАПУСК ПОЛНОЙ ДИАГНОСТИКИ");
    
    // 1. Анализ модального окна
    const modalAnalysis = analyzeModalStructure();
    if (!modalAnalysis) return;
    
    // 2. Анализ первой кнопки "Подписки"
    if (modalAnalysis.followButtons.length === 0) {
        console.error("❌ Кнопки 'Подписки' не найдены!");
        return;
    }
    
    const firstButton = modalAnalysis.followButtons[0];
    console.log("🎯 Анализируем первую кнопку 'Подписки'");
    
    // 3. Анализ структуры строки пользователя
    const userRowAnalysis = analyzeUserRowStructure(firstButton);
    
    // 4. Попробуем извлечь имя пользователя из каждого найденного элемента
    userRowAnalysis.forEach(analysis => {
        if (analysis.found && analysis.element) {
            console.log(`🔍 Попытка извлечения имени из: ${analysis.strategy}`);
            const username = extractUsernameFromElement(analysis.element);
            
            if (username) {
                console.log(`✅ Извлечено имя: ${username}`);
                const exclusionCheck = checkExclusions(username);
                console.log(`🎯 Проверка исключений: ${exclusionCheck.excluded ? '🚨 ИСКЛЮЧЕН' : '✅ РАЗРЕШЕН'}`);
            } else {
                console.log("❌ Имя не извлечено");
            }
        }
    });
}

// Запуск диагностики
runDiagnostics();

// Экспорт функций для ручного использования
window.debugInstagramExclusions = {
    analyzeModalStructure,
    analyzeUserRowStructure,
    extractUsernameFromElement,
    checkExclusions,
    runDiagnostics
};

console.log("🔧 Отладочные функции доступны в window.debugInstagramExclusions");
console.log("📖 Используйте window.debugInstagramExclusions.runDiagnostics() для повторного запуска");
