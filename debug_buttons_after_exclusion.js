// ДИАГНОСТИКА ПРОБЛЕМЫ С КНОПКАМИ ПОСЛЕ ИСКЛЮЧЕНИЙ
// Запустите этот скрипт в консоли браузера для анализа состояния кнопок

console.log("🔍 ДИАГНОСТИКА СОСТОЯНИЯ КНОПОК ПОСЛЕ ИСКЛЮЧЕНИЙ");

const CONFIG = {
    UNFOLLOW_BUTTON_TEXT_RU: "Подписки",
    UNFOLLOW_BUTTON_TEXT_EN: "Following",
    EXCLUDED_USERS: ["e.pavlyuk_", "esipenkoyana93", "julia_figulia", "irok_nu_ti_chego", "pa.vel6627rus"]
};

function analyzeButtonsState() {
    console.log("\n=== АНАЛИЗ СОСТОЯНИЯ КНОПОК ===");
    
    // Найти модальное окно
    const modal = document.querySelector('div[role="dialog"]');
    if (!modal) {
        console.error("❌ Модальное окно не найдено!");
        return;
    }
    
    console.log("✅ Модальное окно найдено");
    
    // Найти все кнопки в модальном окне
    const allButtons = Array.from(modal.querySelectorAll('button'));
    console.log(`📊 Всего кнопок в модальном окне: ${allButtons.length}`);
    
    // Фильтровать кнопки "Подписки"
    const unfollowButtons = allButtons.filter(btn => 
        btn.textContent.includes(CONFIG.UNFOLLOW_BUTTON_TEXT_RU) || 
        btn.textContent.includes(CONFIG.UNFOLLOW_BUTTON_TEXT_EN)
    );
    
    console.log(`🎯 Кнопок "Подписки": ${unfollowButtons.length}`);
    
    // Анализ обработанных и необработанных кнопок
    const processedButtons = unfollowButtons.filter(btn => btn.dataset.processed);
    const unprocessedButtons = unfollowButtons.filter(btn => !btn.dataset.processed);
    
    console.log(`📈 Статистика кнопок:`);
    console.log(`  - Всего кнопок "Подписки": ${unfollowButtons.length}`);
    console.log(`  - Обработанных: ${processedButtons.length}`);
    console.log(`  - Необработанных: ${unprocessedButtons.length}`);
    
    // Детальный анализ каждой кнопки
    console.log("\n=== ДЕТАЛЬНЫЙ АНАЛИЗ КНОПОК ===");
    unfollowButtons.forEach((btn, index) => {
        console.log(`\n🔘 Кнопка ${index + 1}:`);
        console.log(`  - Текст: "${btn.textContent.trim()}"`);
        console.log(`  - Обработана: ${!!btn.dataset.processed}`);
        console.log(`  - Видима: ${btn.offsetParent !== null}`);
        console.log(`  - Активна: ${!btn.disabled}`);
        
        // Попытка извлечь имя пользователя
        const username = extractUsernameFromButton(btn);
        console.log(`  - Извлеченное имя: ${username || 'НЕ УДАЛОСЬ ИЗВЛЕЧЬ'}`);
        
        if (username) {
            const isExcluded = CONFIG.EXCLUDED_USERS.includes(username);
            console.log(`  - Исключен: ${isExcluded ? '🚨 ДА' : '✅ НЕТ'}`);
        }
    });
    
    return {
        total: unfollowButtons.length,
        processed: processedButtons.length,
        unprocessed: unprocessedButtons.length,
        buttons: unfollowButtons
    };
}

function extractUsernameFromButton(button) {
    // Поиск родительского элемента пользователя
    const searchStrategies = [
        () => button.closest('div[style*="padding"]'),
        () => button.closest('[role="listitem"]'),
        () => button.closest('div').closest('div'),
        () => button.parentElement?.parentElement,
        () => button.parentElement?.parentElement?.parentElement,
        () => button.closest('div[class*="x"]'),
        () => {
            let current = button.parentElement;
            for (let i = 0; i < 5 && current; i++) {
                if (current.querySelector('a[href*="/"]')) return current;
                current = current.parentElement;
            }
            return null;
        }
    ];
    
    let userElement = null;
    for (const strategy of searchStrategies) {
        try {
            userElement = strategy();
            if (userElement) break;
        } catch (e) {
            // Игнорируем ошибки
        }
    }
    
    if (!userElement) return null;
    
    // Поиск ссылки на профиль
    const links = Array.from(userElement.querySelectorAll('a'));
    for (const link of links) {
        if (link.href) {
            const regexes = [
                /\/([^\/\?#]+)\/?(?:\?.*)?(?:#.*)?$/,
                /\/([^\/\?#]+)\/?$/,
                /instagram\.com\/([^\/\?#]+)/,
                /\/([a-zA-Z0-9._]+)\/?/
            ];
            
            for (const regex of regexes) {
                const match = link.href.match(regex);
                if (match && match[1] && !match[1].includes('explore') && !match[1].includes('direct')) {
                    return match[1];
                }
            }
        }
    }
    
    return null;
}

function simulateScrollAndCheck() {
    console.log("\n=== СИМУЛЯЦИЯ ПРОКРУТКИ ===");
    
    const modal = document.querySelector('div[role="dialog"]');
    if (!modal) {
        console.error("❌ Модальное окно не найдено!");
        return;
    }
    
    // Найти прокручиваемый элемент
    const scrollableElement = modal.querySelector('div[style*="overflow"]') || 
                             modal.querySelector('.x1r19xyu div[style*="overflow"]') ||
                             modal.querySelector('div.x83m0k');
    
    if (!scrollableElement) {
        console.error("❌ Прокручиваемый элемент не найден!");
        return;
    }
    
    console.log("✅ Прокручиваемый элемент найден");
    
    // Состояние до прокрутки
    const beforeScroll = analyzeButtonsState();
    console.log(`📊 До прокрутки: ${beforeScroll.unprocessed} необработанных кнопок`);
    
    // Выполнить прокрутку
    console.log("🔄 Выполняем прокрутку...");
    scrollableElement.scrollBy({ top: 300, behavior: 'smooth' });
    
    // Ждем и проверяем результат
    setTimeout(() => {
        console.log("\n=== РЕЗУЛЬТАТ ПОСЛЕ ПРОКРУТКИ ===");
        const afterScroll = analyzeButtonsState();
        console.log(`📊 После прокрутки: ${afterScroll.unprocessed} необработанных кнопок`);
        
        const newButtons = afterScroll.unprocessed - beforeScroll.unprocessed;
        if (newButtons > 0) {
            console.log(`✅ Появилось ${newButtons} новых необработанных кнопок`);
        } else if (newButtons === 0) {
            console.log("⚠️ Новые кнопки не появились");
        } else {
            console.log(`❌ Количество необработанных кнопок уменьшилось на ${Math.abs(newButtons)}`);
        }
    }, 2000);
}

function resetAllButtons() {
    console.log("\n=== СБРОС ВСЕХ КНОПОК ===");
    
    const modal = document.querySelector('div[role="dialog"]');
    if (!modal) {
        console.error("❌ Модальное окно не найдено!");
        return;
    }
    
    const allButtons = Array.from(modal.querySelectorAll('button'));
    const unfollowButtons = allButtons.filter(btn => 
        btn.textContent.includes(CONFIG.UNFOLLOW_BUTTON_TEXT_RU) || 
        btn.textContent.includes(CONFIG.UNFOLLOW_BUTTON_TEXT_EN)
    );
    
    let resetCount = 0;
    unfollowButtons.forEach(btn => {
        if (btn.dataset.processed) {
            delete btn.dataset.processed;
            resetCount++;
        }
    });
    
    console.log(`✅ Сброшено ${resetCount} кнопок`);
    console.log("💡 Теперь все кнопки снова доступны для обработки");
    
    // Показать новое состояние
    analyzeButtonsState();
}

// Экспорт функций для использования
window.buttonsDiagnostic = {
    analyzeButtonsState,
    simulateScrollAndCheck,
    resetAllButtons,
    extractUsernameFromButton
};

// Автоматический запуск анализа
console.log("🚀 Запуск автоматического анализа...");
const initialState = analyzeButtonsState();

console.log("\n🔧 Доступные функции:");
console.log("- window.buttonsDiagnostic.analyzeButtonsState() - анализ текущего состояния");
console.log("- window.buttonsDiagnostic.simulateScrollAndCheck() - симуляция прокрутки");
console.log("- window.buttonsDiagnostic.resetAllButtons() - сброс всех кнопок");

if (initialState.unprocessed === 0 && initialState.processed > 0) {
    console.log("\n⚠️ ОБНАРУЖЕНА ПРОБЛЕМА:");
    console.log("Все кнопки помечены как обработанные, но необработанных нет.");
    console.log("Это может быть причиной проблемы с продолжением отписки.");
    console.log("💡 Попробуйте: window.buttonsDiagnostic.resetAllButtons()");
}
