# Инструкция по тестированию исправленного скрипта

## Подготовка к тестированию

### 1. Проверка списка исключений
Убедитесь, что в файле скрипта список `EXCLUDED_USERS` содержит актуальные имена пользователей:
```javascript
EXCLUDED_USERS: ["e.pavlyuk_", "esipenkoyana93", "julia_figulia", "irok_nu_ti_chego", "pa.vel6627rus"]
```

### 2. Открытие консоли браузера
1. Откройте Instagram в браузере
2. Нажмите F12 для открытия инструментов разработчика
3. Перейдите на вкладку "Console" (Консоль)

## Пошаговое тестирование

### Шаг 1: Предварительный тест
1. Откройте файл `test_exclusions.html` в браузере
2. Нажмите все кнопки тестирования
3. Убедитесь, что:
   - Регулярное выражение корректно извлекает имена пользователей
   - Система исключений правильно определяет исключенных пользователей
   - Симуляция показывает ожидаемые результаты

### Шаг 2: Тестирование на Instagram
1. Войдите в свой аккаунт Instagram
2. Перейдите на страницу своего профиля
3. Откройте консоль браузера (F12)
4. Скопируйте и вставьте исправленный скрипт в консоль
5. **ВАЖНО:** Установите максимальное количество отписок = 1 для первого теста

### Шаг 3: Мониторинг работы скрипта
Следите за сообщениями в консоли:

#### Ожидаемые сообщения при обнаружении исключенного пользователя:
```
DEBUG: Начало проверки на исключенного пользователя.
DEBUG: Извлеченное имя пользователя (username): e.pavlyuk_
DEBUG: Пользователь 'e.pavlyuk_' ЕСТЬ в списке исключений.
Пропускаем пользователя из списка исключений: e.pavlyuk_
Пользователь пропущен (в списке исключений).
```

#### Ожидаемые сообщения при обработке обычного пользователя:
```
DEBUG: Начало проверки на исключенного пользователя.
DEBUG: Извлеченное имя пользователя (username): some_user
DEBUG: Пользователь 'some_user' НЕТ в списке исключений.
Нажимаем 'Отписаться'...
```

### Шаг 4: Проверка результатов
1. **Исключенные пользователи НЕ должны быть отписаны**
2. **Обычные пользователи должны быть отписаны**
3. **Счетчик отписок должен увеличиваться только для реально отписанных пользователей**

## Возможные проблемы и решения

### Проблема: "Не удалось найти родительский элемент строки пользователя"
**Решение:** Instagram изменил верстку. Нужно обновить селекторы в конфигурации.

### Проблема: "Не удалось извлечь имя пользователя из ссылки"
**Решение:** 
1. Проверьте URL в отладочных сообщениях
2. При необходимости обновите регулярное выражение
3. Скрипт попытается извлечь имя из текста ссылки как fallback

### Проблема: Исключенный пользователь все равно отписывается
**Решение:**
1. Проверьте точность написания имени в списке `EXCLUDED_USERS`
2. Убедитесь, что имя пользователя корректно извлекается (смотрите DEBUG сообщения)
3. Проверьте, что используется исправленная версия скрипта

## Рекомендации по безопасному тестированию

1. **Начните с 1-2 отписок** для проверки работы системы исключений
2. **Внимательно следите за консолью** - все действия логируются
3. **Проверьте список подписок** после каждого теста
4. **Убедитесь, что исключенные пользователи остались в списке подписок**
5. **Постепенно увеличивайте количество отписок** после успешных тестов

## Контрольный список перед полным запуском

- [ ] Тест регулярного выражения пройден успешно
- [ ] Тест системы исключений показывает корректные результаты
- [ ] Пробный запуск с 1 отпиской работает корректно
- [ ] Исключенные пользователи НЕ отписываются
- [ ] Обычные пользователи отписываются успешно
- [ ] Отладочные сообщения показывают корректную работу
- [ ] Список исключений актуален и проверен

## В случае проблем

Если что-то работает не так, как ожидается:
1. Сохраните логи из консоли браузера
2. Проверьте, что используется исправленная версия скрипта
3. Убедитесь, что список исключений содержит правильные имена пользователей
4. При необходимости обратитесь за помощью с приложением логов
